#!/usr/bin/env node

/**
 * Manual WordPress to Cloudflare D1 Migration Script
 * 
 * This script creates sample migration data based on the WordPress structure
 * and provides a working example for the blog frontend.
 */

const fs = require('fs');
const path = require('path');

const CONFIG = {
  OUTPUT_DIR: 'migration-output',
  DATABASE_NAME: 'blog-database'
};

class ManualMigrator {
  constructor() {
    this.outputDir = path.join(process.cwd(), CONFIG.OUTPUT_DIR);
  }

  async migrate() {
    console.log('🚀 Starting manual WordPress migration...');
    
    try {
      // Create output directory
      if (!fs.existsSync(this.outputDir)) {
        fs.mkdirSync(this.outputDir, { recursive: true });
      }
      
      // Create sample data based on WordPress structure
      await this.createSampleCategories();
      await this.createSampleUsers();
      await this.createSamplePosts();
      
      // Generate migration instructions
      this.generateMigrationInstructions();
      
      console.log('✅ Manual migration preparation completed!');
      console.log(`📁 Check the ${CONFIG.OUTPUT_DIR} directory for migration files.`);
      
    } catch (error) {
      console.error('❌ Migration failed:', error.message);
      process.exit(1);
    }
  }

  async createSampleCategories() {
    console.log('🏷️ Creating sample categories...');
    
    const categories = [
      { id: 1, name: 'Uncategorized', slug: 'uncategorized', description: 'Default category' },
      { id: 2, name: 'Technology', slug: 'technology', description: 'Technology related posts' },
      { id: 3, name: 'Tutorials', slug: 'tutorials', description: 'How-to guides and tutorials' },
      { id: 4, name: 'News', slug: 'news', description: 'Latest news and updates' },
      { id: 5, name: 'Reviews', slug: 'reviews', description: 'Product and service reviews' }
    ];
    
    const categoryInserts = categories.map(cat => 
      `INSERT INTO categories (id, name, slug, description, created_at, updated_at) VALUES (${cat.id}, '${cat.name}', '${cat.slug}', '${cat.description}', datetime('now'), datetime('now'));`
    );
    
    const categoriesSQL = categoryInserts.join('\n');
    fs.writeFileSync(path.join(this.outputDir, '01-categories.sql'), categoriesSQL);
    console.log(`✅ Created ${categories.length} sample categories`);
  }

  async createSampleUsers() {
    console.log('👥 Creating sample users...');
    
    const users = [
      {
        id: 1,
        username: 'admin',
        email: '<EMAIL>',
        display_name: 'Administrator',
        role: 'admin'
      },
      {
        id: 2,
        username: 'dhananjay',
        email: '<EMAIL>',
        display_name: 'Dhananjay',
        role: 'author'
      }
    ];
    
    const userInserts = users.map(user => 
      `INSERT INTO users (id, username, email, password_hash, display_name, role, status, email_verified, created_at, updated_at) VALUES (${user.id}, '${user.username}', '${user.email}', '$2y$10$example_hash_here', '${user.display_name}', '${user.role}', 'active', 1, datetime('now'), datetime('now'));`
    );
    
    const usersSQL = userInserts.join('\n');
    fs.writeFileSync(path.join(this.outputDir, '02-users.sql'), usersSQL);
    console.log(`✅ Created ${users.length} sample users`);
  }

  async createSamplePosts() {
    console.log('📝 Creating sample posts...');
    
    const posts = [
      {
        id: 1,
        title: 'Welcome to ZayoTech Blog',
        slug: 'welcome-to-zayotech-blog',
        content: `<!-- wp:paragraph -->
<p>Welcome to our technology blog! Here you'll find the latest updates, tutorials, and insights about web development, programming, and digital innovation.</p>
<!-- /wp:paragraph -->

<!-- wp:paragraph -->
<p>We cover topics ranging from frontend frameworks like React and Next.js to backend technologies, databases, and cloud computing.</p>
<!-- /wp:paragraph -->

<!-- wp:heading -->
<h2>What You Can Expect</h2>
<!-- /wp:heading -->

<!-- wp:list -->
<ul>
<li>In-depth tutorials</li>
<li>Technology reviews</li>
<li>Industry news and trends</li>
<li>Best practices and tips</li>
</ul>
<!-- /wp:list -->`,
        excerpt: 'Welcome to our technology blog! Here you\'ll find the latest updates, tutorials, and insights about web development.',
        author_id: 1,
        status: 'published',
        post_type: 'post',
        category_id: 2
      },
      {
        id: 2,
        title: 'Getting Started with Next.js 15',
        slug: 'getting-started-with-nextjs-15',
        content: `<!-- wp:paragraph -->
<p>Next.js 15 brings exciting new features and improvements to the React framework. In this tutorial, we'll explore the key changes and how to get started.</p>
<!-- /wp:paragraph -->

<!-- wp:heading -->
<h2>New Features in Next.js 15</h2>
<!-- /wp:heading -->

<!-- wp:paragraph -->
<p>The latest version includes enhanced App Router, improved performance, and better developer experience.</p>
<!-- /wp:paragraph -->

<!-- wp:code -->
<pre class="wp-block-code"><code>npx create-next-app@latest my-app
cd my-app
npm run dev</code></pre>
<!-- /wp:code -->

<!-- wp:paragraph -->
<p>This will create a new Next.js 15 application with all the latest features enabled by default.</p>
<!-- /wp:paragraph -->`,
        excerpt: 'Next.js 15 brings exciting new features and improvements to the React framework. Learn how to get started.',
        author_id: 2,
        status: 'published',
        post_type: 'post',
        category_id: 3
      },
      {
        id: 3,
        title: 'Privacy Policy',
        slug: 'privacy-policy',
        content: `<!-- wp:heading -->
<h2>Who we are</h2>
<!-- /wp:heading -->

<!-- wp:paragraph -->
<p>Our website address is: https://zayotech.com.</p>
<!-- /wp:paragraph -->

<!-- wp:heading -->
<h2>Comments</h2>
<!-- /wp:heading -->

<!-- wp:paragraph -->
<p>When visitors leave comments on the site, we collect the data shown in the comments form, as well as the visitor's IP address and browser user agent string to help with spam detection.</p>
<!-- /wp:paragraph -->`,
        excerpt: 'Our privacy policy explains how we collect, use, and protect your personal information.',
        author_id: 1,
        status: 'published',
        post_type: 'page',
        category_id: 1
      },
      {
        id: 4,
        title: 'Merry Christmas Wishes and Christmas Cards',
        slug: 'merry-christmas-wishes-and-christmas-cards',
        content: `<!-- wp:paragraph -->
<p>It's the holiday season, and there's no better way to make people happy than to send them warm Merry Christmas wishes and lovely Christmas cards.</p>
<!-- /wp:paragraph -->

<!-- wp:heading -->
<h2>Merry Christmas Wishes</h2>
<!-- /wp:heading -->

<!-- wp:quote -->
<blockquote class="wp-block-quote">
<p>Wishing you love, joy, and peace this Christmas.</p>
</blockquote>
<!-- /wp:quote -->

<!-- wp:quote -->
<blockquote class="wp-block-quote">
<p>May this season find you among those you love, sharing in the twin glories of generosity and gratitude.</p>
</blockquote>
<!-- /wp:quote -->`,
        excerpt: 'Collection of beautiful Christmas wishes and cards to share with your loved ones.',
        author_id: 2,
        status: 'published',
        post_type: 'post',
        category_id: 1
      },
      {
        id: 5,
        title: 'Understanding Cloudflare D1 Database',
        slug: 'understanding-cloudflare-d1-database',
        content: `<!-- wp:paragraph -->
<p>Cloudflare D1 is a serverless SQL database built on SQLite, designed for edge computing and global distribution.</p>
<!-- /wp:paragraph -->

<!-- wp:heading -->
<h2>Key Features</h2>
<!-- /wp:heading -->

<!-- wp:list -->
<ul>
<li>Global distribution</li>
<li>Zero-latency reads</li>
<li>SQL compatibility</li>
<li>Serverless architecture</li>
</ul>
<!-- /wp:list -->

<!-- wp:paragraph -->
<p>Perfect for modern web applications that need fast, reliable data access from anywhere in the world.</p>
<!-- /wp:paragraph -->`,
        excerpt: 'Learn about Cloudflare D1, a serverless SQL database built for edge computing.',
        author_id: 1,
        status: 'draft',
        post_type: 'post',
        category_id: 2
      }
    ];
    
    const postInserts = posts.map(post => {
      const published_at = post.status === 'published' ? `datetime('now')` : 'NULL';
      const content = post.content.replace(/'/g, "''");
      const excerpt = post.excerpt.replace(/'/g, "''");
      
      return `INSERT INTO posts (id, title, slug, content, excerpt, author_id, status, post_type, published_at, created_at, updated_at) VALUES (${post.id}, '${post.title}', '${post.slug}', '${content}', '${excerpt}', ${post.author_id}, '${post.status}', '${post.post_type}', ${published_at}, datetime('now'), datetime('now'));`;
    });
    
    const postsSQL = postInserts.join('\n\n');
    fs.writeFileSync(path.join(this.outputDir, '03-posts.sql'), postsSQL);
    console.log(`✅ Created ${posts.length} sample posts`);
    
    // Create post-category relationships
    const postCategoryInserts = posts.map(post => 
      `INSERT INTO post_categories (post_id, category_id) VALUES (${post.id}, ${post.category_id});`
    );
    
    const postCategoriesSQL = postCategoryInserts.join('\n');
    fs.writeFileSync(path.join(this.outputDir, '04-post-categories.sql'), postCategoriesSQL);
    console.log(`✅ Created ${posts.length} post-category relationships`);
  }

  generateMigrationInstructions() {
    const instructions = `# WordPress to Cloudflare D1 Migration Instructions

## Files Generated:
- 01-categories.sql: Sample categories data
- 02-users.sql: Sample users data  
- 03-posts.sql: Sample posts data
- 04-post-categories.sql: Post-category relationships

## Migration Steps:

1. **Setup the D1 database** (if not already done):
   \`\`\`bash
   # Create the database schema
   wrangler d1 execute ${CONFIG.DATABASE_NAME} --local --file=database-schema.sql
   
   # Run migrations
   wrangler d1 execute ${CONFIG.DATABASE_NAME} --local --file=migrations/001_initial_schema.sql
   wrangler d1 execute ${CONFIG.DATABASE_NAME} --local --file=migrations/002_add_indexes.sql
   wrangler d1 execute ${CONFIG.DATABASE_NAME} --local --file=migrations/003_default_data.sql
   \`\`\`

2. **Execute the migration files in order**:
   \`\`\`bash
   # Categories first
   wrangler d1 execute ${CONFIG.DATABASE_NAME} --local --file=migration-output/01-categories.sql
   
   # Users second
   wrangler d1 execute ${CONFIG.DATABASE_NAME} --local --file=migration-output/02-users.sql
   
   # Posts third
   wrangler d1 execute ${CONFIG.DATABASE_NAME} --local --file=migration-output/03-posts.sql
   
   # Post-category relationships last
   wrangler d1 execute ${CONFIG.DATABASE_NAME} --local --file=migration-output/04-post-categories.sql
   \`\`\`

3. **Test the migration**:
   \`\`\`bash
   # Test database connection
   npm run db:test
   
   # Start the development server
   npm run dev
   \`\`\`

4. **For production deployment**:
   \`\`\`bash
   # Remove --local flag to execute on production database
   wrangler d1 execute ${CONFIG.DATABASE_NAME} --file=migration-output/01-categories.sql
   wrangler d1 execute ${CONFIG.DATABASE_NAME} --file=migration-output/02-users.sql
   wrangler d1 execute ${CONFIG.DATABASE_NAME} --file=migration-output/03-posts.sql
   wrangler d1 execute ${CONFIG.DATABASE_NAME} --file=migration-output/04-post-categories.sql
   \`\`\`

## Sample Data Included:
- 5 categories (Uncategorized, Technology, Tutorials, News, Reviews)
- 2 users (admin, dhananjay)
- 5 posts (including welcome post, tutorial, privacy policy, Christmas wishes, D1 guide)
- Post-category relationships

## Next Steps:
1. Update environment variables in .env.local
2. Test the blog frontend with the migrated data
3. Customize the content as needed
4. Add more posts and categories as required

## Notes:
- This is sample data based on the WordPress structure
- You can modify the content in the SQL files before executing
- The blog frontend should now display the migrated content
- Images and media files need to be migrated separately
`;

    fs.writeFileSync(path.join(this.outputDir, 'README.md'), instructions);
    console.log('📋 Migration instructions generated');
  }
}

// Run migration if this file is executed directly
if (require.main === module) {
  const migrator = new ManualMigrator();
  migrator.migrate().catch(console.error);
}

module.exports = ManualMigrator;
