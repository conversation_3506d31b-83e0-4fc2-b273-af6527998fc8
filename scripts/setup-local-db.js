#!/usr/bin/env node

/**
 * Setup local SQLite database for development
 * This script creates a local SQLite database that mirrors the D1 database structure
 * and imports the migrated data for local development.
 */

const Database = require('better-sqlite3');
const fs = require('fs');
const path = require('path');

const DB_PATH = path.join(__dirname, '..', 'local-database.sqlite');
const SCHEMA_PATH = path.join(__dirname, '..', 'database-schema.sql');
const MIGRATION_OUTPUT_DIR = path.join(__dirname, '..', 'migration-output');

console.log('🚀 Setting up local SQLite database for development...');

// Remove existing database
if (fs.existsSync(DB_PATH)) {
  fs.unlinkSync(DB_PATH);
  console.log('🗑️  Removed existing database');
}

// Create new database
const db = new Database(DB_PATH);
console.log('📁 Created new SQLite database at:', DB_PATH);

// Read and execute schema
console.log('📋 Creating database schema...');
const schema = fs.readFileSync(SCHEMA_PATH, 'utf8');

// Split schema into individual statements and execute
const statements = schema.split(';').filter(stmt => stmt.trim().length > 0);
for (const statement of statements) {
  try {
    db.exec(statement + ';');
  } catch (error) {
    console.warn('⚠️  Warning executing statement:', error.message);
  }
}

console.log('✅ Database schema created successfully');

// Import migrated data
console.log('📥 Importing migrated data...');

const migrationFiles = [
  '01-categories.sql',
  '02-users.sql', 
  '03-posts.sql',
  '04-post-categories.sql'
];

for (const filename of migrationFiles) {
  const filePath = path.join(MIGRATION_OUTPUT_DIR, filename);
  if (fs.existsSync(filePath)) {
    console.log(`📄 Importing ${filename}...`);
    const sql = fs.readFileSync(filePath, 'utf8');
    
    // Split into individual INSERT statements
    const insertStatements = sql.split(';').filter(stmt => stmt.trim().length > 0);
    for (const statement of insertStatements) {
      try {
        db.exec(statement + ';');
      } catch (error) {
        console.warn(`⚠️  Warning importing ${filename}:`, error.message);
      }
    }
  } else {
    console.warn(`⚠️  Migration file not found: ${filename}`);
  }
}

// Add some default settings
console.log('⚙️  Adding default settings...');
const settingsData = [
  ['site_title', 'ZayoTech Blog'],
  ['site_description', 'Inspirational quotes, shayari, and stories in Hindi and English'],
  ['site_url', 'https://your-domain.com'],
  ['posts_per_page', '10'],
  ['comment_moderation', 'false'],
  ['default_language', 'en'],
  ['theme', 'teknorial'],
  ['timezone', 'UTC']
];

const insertSetting = db.prepare('INSERT OR REPLACE INTO settings (key, value, updated_at) VALUES (?, ?, CURRENT_TIMESTAMP)');

for (const [key, value] of settingsData) {
  insertSetting.run(key, value);
}

// Verify data
console.log('🔍 Verifying imported data...');
const categoriesCount = db.prepare('SELECT COUNT(*) as count FROM categories').get();
const postsCount = db.prepare('SELECT COUNT(*) as count FROM posts').get();
const usersCount = db.prepare('SELECT COUNT(*) as count FROM users').get();
const settingsCount = db.prepare('SELECT COUNT(*) as count FROM settings').get();

console.log(`📊 Data verification:
  - Categories: ${categoriesCount.count}
  - Posts: ${postsCount.count}
  - Users: ${usersCount.count}
  - Settings: ${settingsCount.count}`);

// Close database
db.close();

console.log('✅ Local SQLite database setup completed successfully!');
console.log(`📍 Database location: ${DB_PATH}`);
console.log('🔧 You can now run the development server with local database access.');
