#!/usr/bin/env node

/**
 * Simple WordPress to Cloudflare D1 Migration Script
 * 
 * This script extracts key data from WordPress SQL dump and creates
 * D1-compatible SQL statements for manual execution.
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

const CONFIG = {
  WORDPRESS_SQL_FILE: 'u957990218_GpBKT.zayotech-com.20250727190356.sql',
  OUTPUT_DIR: 'migration-output',
  DATABASE_NAME: 'blog-database'
};

class SimpleMigrator {
  constructor() {
    this.sqlContent = '';
    this.outputDir = path.join(process.cwd(), CONFIG.OUTPUT_DIR);
  }

  async migrate() {
    console.log('🚀 Starting simple WordPress migration...');
    
    try {
      // Create output directory
      if (!fs.existsSync(this.outputDir)) {
        fs.mkdirSync(this.outputDir, { recursive: true });
      }
      
      // Read WordPress SQL file
      this.readSQLFile();
      
      // Extract and convert data
      await this.extractCategories();
      await this.extractUsers();
      await this.extractPosts();
      
      // Generate migration instructions
      this.generateMigrationInstructions();
      
      console.log('✅ Migration preparation completed!');
      console.log(`📁 Check the ${CONFIG.OUTPUT_DIR} directory for migration files.`);
      
    } catch (error) {
      console.error('❌ Migration failed:', error.message);
      process.exit(1);
    }
  }

  readSQLFile() {
    console.log('📖 Reading WordPress SQL file...');
    const sqlFilePath = path.join(process.cwd(), CONFIG.WORDPRESS_SQL_FILE);
    
    if (!fs.existsSync(sqlFilePath)) {
      throw new Error(`WordPress SQL file not found: ${sqlFilePath}`);
    }
    
    this.sqlContent = fs.readFileSync(sqlFilePath, 'utf8');
    console.log(`✅ SQL file loaded (${(this.sqlContent.length / 1024 / 1024).toFixed(2)} MB)`);
  }

  async extractCategories() {
    console.log('🏷️ Extracting categories...');
    
    // Extract categories from wp_terms
    const categoryInserts = [];
    const termRegex = /INSERT INTO `wp_terms` VALUES\s*\((.*?)\);/gs;
    let match;
    
    while ((match = termRegex.exec(this.sqlContent)) !== null) {
      const valuesString = match[1];
      
      // Parse the basic category data
      const categories = this.parseBasicInsertValues(valuesString);
      
      categories.forEach((cat, index) => {
        if (cat.length >= 3) {
          const id = cat[0];
          const name = cat[1].replace(/'/g, "''"); // Escape quotes for SQL
          const slug = cat[2].replace(/'/g, "''");
          
          categoryInserts.push(
            `INSERT INTO categories (id, name, slug, description, created_at, updated_at) VALUES (${id}, '${name}', '${slug}', '', datetime('now'), datetime('now'));`
          );
        }
      });
    }
    
    // Write categories SQL
    const categoriesSQL = categoryInserts.join('\n');
    fs.writeFileSync(path.join(this.outputDir, '01-categories.sql'), categoriesSQL);
    console.log(`✅ Extracted ${categoryInserts.length} categories`);
  }

  async extractUsers() {
    console.log('👥 Extracting users...');
    
    const userInserts = [];
    const userRegex = /INSERT INTO `wp_users` VALUES\s*\((.*?)\);/gs;
    let match;
    
    while ((match = userRegex.exec(this.sqlContent)) !== null) {
      const valuesString = match[1];
      const users = this.parseBasicInsertValues(valuesString);
      
      users.forEach(user => {
        if (user.length >= 5) {
          const id = user[0];
          const username = user[1].replace(/'/g, "''");
          const password_hash = user[2].replace(/'/g, "''");
          const email = user[4].replace(/'/g, "''");
          const display_name = user[3].replace(/'/g, "''") || username;
          
          userInserts.push(
            `INSERT INTO users (id, username, email, password_hash, display_name, role, status, email_verified, created_at, updated_at) VALUES (${id}, '${username}', '${email}', '${password_hash}', '${display_name}', 'author', 'active', 1, datetime('now'), datetime('now'));`
          );
        }
      });
    }
    
    const usersSQL = userInserts.join('\n');
    fs.writeFileSync(path.join(this.outputDir, '02-users.sql'), usersSQL);
    console.log(`✅ Extracted ${userInserts.length} users`);
  }

  async extractPosts() {
    console.log('📝 Extracting posts...');

    const postInserts = [];

    // Find the wp_posts INSERT section
    const postSectionRegex = /INSERT INTO `wp_posts` VALUES\s*\n([\s\S]*?);\s*\n!/;
    const postSectionMatch = this.sqlContent.match(postSectionRegex);

    if (!postSectionMatch) {
      console.log('❌ Could not find wp_posts INSERT section');
      return;
    }

    const postData = postSectionMatch[1];

    // Split by lines and process each post record
    const lines = postData.split('\n');
    let currentPost = '';
    let inPost = false;

    for (const line of lines) {
      const trimmedLine = line.trim();

      if (trimmedLine.startsWith('(')) {
        // Start of a new post
        inPost = true;
        currentPost = trimmedLine;
      } else if (inPost) {
        currentPost += ' ' + trimmedLine;
      }

      // Check if this line ends a post record
      if (inPost && (trimmedLine.endsWith('),') || trimmedLine.endsWith(')'))) {
        // Process this post
        this.processPostRecord(currentPost, postInserts);
        currentPost = '';
        inPost = false;
      }
    }

    const postsSQL = postInserts.join('\n');
    fs.writeFileSync(path.join(this.outputDir, '03-posts.sql'), postsSQL);
    console.log(`✅ Extracted ${postInserts.length} posts`);
  }

  processPostRecord(postRecord, postInserts) {
    try {
      // Clean up the record
      let cleanRecord = postRecord.trim();
      if (cleanRecord.startsWith('(')) cleanRecord = cleanRecord.substring(1);
      if (cleanRecord.endsWith('),')) cleanRecord = cleanRecord.slice(0, -2);
      if (cleanRecord.endsWith(')')) cleanRecord = cleanRecord.slice(0, -1);

      // Parse the post data using a more robust method
      const postData = this.parsePostData(cleanRecord);

      if (postData && postData.length >= 20) {
        const id = postData[0];
        const author_id = postData[1];
        const post_date = postData[2];
        const content = postData[4] ? this.escapeSQL(postData[4]) : '';
        const title = postData[5] ? this.escapeSQL(postData[5]) : '';
        const excerpt = postData[6] ? this.escapeSQL(postData[6]) : '';
        const status = this.mapStatus(postData[7]);
        const slug = postData[12] ? this.escapeSQL(postData[12]) : '';
        const post_type = postData[20] || 'post';

        // Only migrate actual posts and pages with valid data
        if ((post_type === 'post' || post_type === 'page') &&
            (status === 'published' || status === 'draft') &&
            title && slug && title !== '' && slug !== '') {

          const published_at = status === 'published' ? `'${post_date}'` : 'NULL';

          postInserts.push(
            `INSERT INTO posts (id, title, slug, content, excerpt, author_id, status, post_type, published_at, created_at, updated_at) VALUES (${id}, '${title}', '${slug}', '${content}', '${excerpt}', ${author_id}, '${status}', '${post_type}', ${published_at}, '${post_date}', '${post_date}');`
          );
        }
      }
    } catch (error) {
      console.log(`⚠️ Error processing post record: ${error.message}`);
    }
  }

  parsePostData(recordString) {
    const values = [];
    let current = '';
    let inQuotes = false;
    let quoteChar = '';
    let depth = 0;

    for (let i = 0; i < recordString.length; i++) {
      const char = recordString[i];
      const prevChar = i > 0 ? recordString[i - 1] : '';

      if ((char === '"' || char === "'") && prevChar !== '\\') {
        if (!inQuotes) {
          inQuotes = true;
          quoteChar = char;
        } else if (char === quoteChar) {
          inQuotes = false;
          quoteChar = '';
        }
      }

      if (char === '(' && !inQuotes) depth++;
      if (char === ')' && !inQuotes) depth--;

      if (char === ',' && !inQuotes && depth === 0) {
        values.push(this.cleanValue(current));
        current = '';
      } else {
        current += char;
      }
    }

    // Add the last value
    if (current) {
      values.push(this.cleanValue(current));
    }

    return values;
  }

  escapeSQL(value) {
    if (!value) return '';
    return value.replace(/'/g, "''").replace(/\\/g, '\\\\');
  }

  parseBasicInsertValues(valuesString) {
    // Simple parser for basic INSERT VALUES
    const records = [];
    
    // Split by '),(' to separate records
    const recordStrings = valuesString.split('),(');
    
    recordStrings.forEach((recordString, index) => {
      // Clean up the record string
      let cleanRecord = recordString.trim();
      if (index === 0 && cleanRecord.startsWith('(')) {
        cleanRecord = cleanRecord.substring(1);
      }
      if (index === recordStrings.length - 1 && cleanRecord.endsWith(')')) {
        cleanRecord = cleanRecord.slice(0, -1);
      }
      
      // Simple split by comma (this won't handle complex cases perfectly)
      const values = this.splitValues(cleanRecord);
      records.push(values);
    });
    
    return records;
  }

  splitValues(recordString) {
    const values = [];
    let current = '';
    let inQuotes = false;
    let quoteChar = '';
    
    for (let i = 0; i < recordString.length; i++) {
      const char = recordString[i];
      const prevChar = i > 0 ? recordString[i - 1] : '';
      
      if ((char === '"' || char === "'") && prevChar !== '\\') {
        if (!inQuotes) {
          inQuotes = true;
          quoteChar = char;
        } else if (char === quoteChar) {
          inQuotes = false;
          quoteChar = '';
        }
      }
      
      if (char === ',' && !inQuotes) {
        values.push(this.cleanValue(current));
        current = '';
      } else {
        current += char;
      }
    }
    
    // Add the last value
    if (current) {
      values.push(this.cleanValue(current));
    }
    
    return values;
  }

  cleanValue(value) {
    const trimmed = value.trim();
    
    if (trimmed === 'NULL') return null;
    
    // Remove quotes
    if ((trimmed.startsWith("'") && trimmed.endsWith("'")) ||
        (trimmed.startsWith('"') && trimmed.endsWith('"'))) {
      return trimmed.slice(1, -1);
    }
    
    return trimmed;
  }

  mapStatus(wpStatus) {
    const statusMap = {
      'publish': 'published',
      'draft': 'draft',
      'private': 'private',
      'trash': 'trash',
      'inherit': 'draft'
    };
    
    return statusMap[wpStatus] || 'draft';
  }

  generateMigrationInstructions() {
    const instructions = `# WordPress to Cloudflare D1 Migration Instructions

## Files Generated:
- 01-categories.sql: Categories data
- 02-users.sql: Users data  
- 03-posts.sql: Posts data

## Migration Steps:

1. **Backup your current D1 database** (if it has data):
   \`\`\`bash
   wrangler d1 execute ${CONFIG.DATABASE_NAME} --local --command="SELECT * FROM posts;" > backup.sql
   \`\`\`

2. **Execute the migration files in order**:
   \`\`\`bash
   # Categories first
   wrangler d1 execute ${CONFIG.DATABASE_NAME} --local --file=migration-output/01-categories.sql
   
   # Users second
   wrangler d1 execute ${CONFIG.DATABASE_NAME} --local --file=migration-output/02-users.sql
   
   # Posts last (they reference users and categories)
   wrangler d1 execute ${CONFIG.DATABASE_NAME} --local --file=migration-output/03-posts.sql
   \`\`\`

3. **Test the migration**:
   \`\`\`bash
   npm run test:db
   \`\`\`

4. **For production deployment**:
   \`\`\`bash
   # Remove --local flag to execute on production database
   wrangler d1 execute ${CONFIG.DATABASE_NAME} --file=migration-output/01-categories.sql
   wrangler d1 execute ${CONFIG.DATABASE_NAME} --file=migration-output/02-users.sql
   wrangler d1 execute ${CONFIG.DATABASE_NAME} --file=migration-output/03-posts.sql
   \`\`\`

## Notes:
- Review the generated SQL files before executing
- Some data may need manual cleanup
- WordPress shortcodes and HTML may need processing
- Image URLs may need updating to point to your new hosting
`;

    fs.writeFileSync(path.join(this.outputDir, 'README.md'), instructions);
    console.log('📋 Migration instructions generated');
  }
}

// Run migration if this file is executed directly
if (require.main === module) {
  const migrator = new SimpleMigrator();
  migrator.migrate().catch(console.error);
}

module.exports = SimpleMigrator;
