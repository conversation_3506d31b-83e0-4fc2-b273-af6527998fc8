#!/usr/bin/env node

/**
 * WordPress to Cloudflare D1 Migration Script
 * 
 * This script migrates data from a WordPress MySQL database dump
 * to the Cloudflare D1 SQLite database schema.
 * 
 * Usage: node scripts/wordpress-migration.js
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

// Configuration
const CONFIG = {
  WORDPRESS_SQL_FILE: 'u957990218_GpBKT.zayotech-com.20250727190356.sql',
  DATABASE_NAME: 'blog-database',
  BATCH_SIZE: 50, // Number of records to process at once
  DRY_RUN: false, // Set to true to test without actually inserting data
};

// WordPress to D1 field mappings
const FIELD_MAPPINGS = {
  posts: {
    'ID': 'id',
    'post_title': 'title',
    'post_name': 'slug',
    'post_content': 'content',
    'post_excerpt': 'excerpt',
    'post_author': 'author_id',
    'post_status': 'status',
    'post_type': 'post_type',
    'post_date': 'published_at',
    'post_date_gmt': 'created_at',
    'post_modified': 'updated_at',
    'comment_count': 'comment_count'
  },
  users: {
    'ID': 'id',
    'user_login': 'username',
    'user_email': 'email',
    'user_pass': 'password_hash',
    'display_name': 'display_name',
    'user_registered': 'created_at'
  },
  terms: {
    'term_id': 'id',
    'name': 'name',
    'slug': 'slug'
  }
};

// Status mappings
const STATUS_MAPPINGS = {
  'publish': 'published',
  'draft': 'draft',
  'private': 'private',
  'trash': 'trash',
  'inherit': 'draft'
};

class WordPressMigrator {
  constructor() {
    this.sqlContent = '';
    this.extractedData = {
      posts: [],
      users: [],
      categories: [],
      tags: [],
      postCategories: [],
      postTags: []
    };
  }

  /**
   * Main migration process
   */
  async migrate() {
    console.log('🚀 Starting WordPress to Cloudflare D1 migration...');
    
    try {
      // Step 1: Read WordPress SQL file
      await this.readWordPressSQLFile();
      
      // Step 2: Extract data from SQL
      await this.extractWordPressData();
      
      // Step 3: Transform data to D1 format
      await this.transformData();
      
      // Step 4: Insert data into D1 database
      if (!CONFIG.DRY_RUN) {
        await this.insertDataToD1();
      } else {
        console.log('🧪 DRY RUN: Data transformation completed. No data inserted.');
        this.printSummary();
      }
      
      console.log('✅ Migration completed successfully!');
      
    } catch (error) {
      console.error('❌ Migration failed:', error.message);
      process.exit(1);
    }
  }

  /**
   * Read WordPress SQL dump file
   */
  async readWordPressSQLFile() {
    console.log('📖 Reading WordPress SQL file...');
    
    const sqlFilePath = path.join(process.cwd(), CONFIG.WORDPRESS_SQL_FILE);
    
    if (!fs.existsSync(sqlFilePath)) {
      throw new Error(`WordPress SQL file not found: ${sqlFilePath}`);
    }
    
    this.sqlContent = fs.readFileSync(sqlFilePath, 'utf8');
    console.log(`✅ SQL file loaded (${(this.sqlContent.length / 1024 / 1024).toFixed(2)} MB)`);
  }

  /**
   * Extract data from WordPress SQL dump
   */
  async extractWordPressData() {
    console.log('🔍 Extracting WordPress data...');
    
    // Extract posts
    this.extractPosts();
    
    // Extract users
    this.extractUsers();
    
    // Extract categories and tags
    this.extractTerms();
    
    // Extract post-category and post-tag relationships
    this.extractTermRelationships();
    
    console.log('✅ Data extraction completed');
  }

  /**
   * Extract posts from wp_posts table
   */
  extractPosts() {
    console.log('📝 Extracting posts...');
    
    // Find INSERT statements for wp_posts
    const postInsertRegex = /INSERT INTO `wp_posts` VALUES\s*\((.*?)\);/gs;
    let match;
    
    while ((match = postInsertRegex.exec(this.sqlContent)) !== null) {
      const valuesString = match[1];
      const posts = this.parseInsertValues(valuesString);
      
      posts.forEach(post => {
        // Only include actual posts and pages, not revisions or other types
        if (post.post_type === 'post' || post.post_type === 'page') {
          if (post.post_status === 'publish' || post.post_status === 'draft') {
            this.extractedData.posts.push(post);
          }
        }
      });
    }
    
    console.log(`📝 Found ${this.extractedData.posts.length} posts`);
  }

  /**
   * Extract users from wp_users table
   */
  extractUsers() {
    console.log('👥 Extracting users...');
    
    const userInsertRegex = /INSERT INTO `wp_users` VALUES\s*\((.*?)\);/gs;
    let match;
    
    while ((match = userInsertRegex.exec(this.sqlContent)) !== null) {
      const valuesString = match[1];
      const users = this.parseInsertValues(valuesString);
      
      users.forEach(user => {
        this.extractedData.users.push(user);
      });
    }
    
    console.log(`👥 Found ${this.extractedData.users.length} users`);
  }

  /**
   * Extract categories and tags from wp_terms and wp_term_taxonomy
   */
  extractTerms() {
    console.log('🏷️ Extracting categories and tags...');
    
    // This is a simplified extraction - in a real migration,
    // you'd need to join wp_terms with wp_term_taxonomy
    const termInsertRegex = /INSERT INTO `wp_terms` VALUES\s*\((.*?)\);/gs;
    let match;
    
    while ((match = termInsertRegex.exec(this.sqlContent)) !== null) {
      const valuesString = match[1];
      const terms = this.parseInsertValues(valuesString);
      
      terms.forEach(term => {
        // For simplicity, treating all terms as categories
        // In a real migration, you'd check wp_term_taxonomy
        this.extractedData.categories.push(term);
      });
    }
    
    console.log(`🏷️ Found ${this.extractedData.categories.length} categories`);
  }

  /**
   * Extract post-term relationships
   */
  extractTermRelationships() {
    console.log('🔗 Extracting term relationships...');
    // This would extract from wp_term_relationships table
    // For now, we'll skip this complex relationship mapping
    console.log('🔗 Term relationships extraction skipped (complex mapping required)');
  }

  /**
   * Parse INSERT VALUES string into array of objects
   */
  parseInsertValues(valuesString) {
    const records = [];

    // Handle multi-line INSERT statements
    const lines = valuesString.split('\n');
    let currentRecord = '';
    let inQuotes = false;
    let quoteChar = '';

    for (const line of lines) {
      for (let i = 0; i < line.length; i++) {
        const char = line[i];
        const prevChar = i > 0 ? line[i - 1] : '';

        if ((char === '"' || char === "'") && prevChar !== '\\') {
          if (!inQuotes) {
            inQuotes = true;
            quoteChar = char;
          } else if (char === quoteChar) {
            inQuotes = false;
            quoteChar = '';
          }
        }

        currentRecord += char;

        // Check for record separator
        if (!inQuotes && char === ')' && line[i + 1] === ',') {
          // End of record
          records.push(this.parseRecordValues(currentRecord));
          currentRecord = '';
          i++; // Skip the comma
        }
      }
    }

    // Handle last record
    if (currentRecord.trim()) {
      records.push(this.parseRecordValues(currentRecord));
    }

    return records;
  }

  /**
   * Parse individual record values with proper SQL parsing
   */
  parseRecordValues(recordString) {
    // Remove leading/trailing parentheses
    let cleaned = recordString.trim();
    if (cleaned.startsWith('(')) cleaned = cleaned.substring(1);
    if (cleaned.endsWith(')')) cleaned = cleaned.slice(0, -1);

    const values = [];
    let current = '';
    let inQuotes = false;
    let quoteChar = '';

    for (let i = 0; i < cleaned.length; i++) {
      const char = cleaned[i];
      const prevChar = i > 0 ? cleaned[i - 1] : '';

      if ((char === '"' || char === "'") && prevChar !== '\\') {
        if (!inQuotes) {
          inQuotes = true;
          quoteChar = char;
        } else if (char === quoteChar) {
          inQuotes = false;
          quoteChar = '';
        }
        current += char;
      } else if (char === ',' && !inQuotes) {
        values.push(this.cleanValue(current));
        current = '';
      } else {
        current += char;
      }
    }

    // Add the last value
    if (current) {
      values.push(this.cleanValue(current));
    }

    return values;
  }

  /**
   * Clean and convert SQL value to appropriate type
   */
  cleanValue(value) {
    const trimmed = value.trim();

    // Handle NULL
    if (trimmed === 'NULL') return null;

    // Handle quoted strings
    if ((trimmed.startsWith("'") && trimmed.endsWith("'")) ||
        (trimmed.startsWith('"') && trimmed.endsWith('"'))) {
      return trimmed.slice(1, -1).replace(/\\'/g, "'").replace(/\\"/g, '"');
    }

    // Handle numbers
    if (/^\d+$/.test(trimmed)) return parseInt(trimmed);
    if (/^\d+\.\d+$/.test(trimmed)) return parseFloat(trimmed);

    return trimmed;
  }

  /**
   * Transform WordPress data to D1 schema format
   */
  async transformData() {
    console.log('🔄 Transforming data to D1 format...');
    
    // Transform posts
    this.transformPosts();
    
    // Transform users
    this.transformUsers();
    
    // Transform categories
    this.transformCategories();
    
    console.log('✅ Data transformation completed');
  }

  /**
   * Transform posts to D1 format
   */
  transformPosts() {
    // Implementation would transform WordPress posts to D1 posts format
    console.log('📝 Transforming posts...');
  }

  /**
   * Transform users to D1 format
   */
  transformUsers() {
    // Implementation would transform WordPress users to D1 users format
    console.log('👥 Transforming users...');
  }

  /**
   * Transform categories to D1 format
   */
  transformCategories() {
    // Implementation would transform WordPress categories to D1 categories format
    console.log('🏷️ Transforming categories...');
  }

  /**
   * Insert transformed data into D1 database
   */
  async insertDataToD1() {
    console.log('💾 Inserting data into Cloudflare D1...');
    
    try {
      // Insert users first (posts reference users)
      await this.insertUsers();
      
      // Insert categories
      await this.insertCategories();
      
      // Insert posts
      await this.insertPosts();
      
      console.log('✅ Data insertion completed');
      
    } catch (error) {
      console.error('❌ Data insertion failed:', error.message);
      throw error;
    }
  }

  /**
   * Insert users into D1
   */
  async insertUsers() {
    console.log('👥 Inserting users...');
    // Implementation would insert users using wrangler d1 execute
  }

  /**
   * Insert categories into D1
   */
  async insertCategories() {
    console.log('🏷️ Inserting categories...');
    // Implementation would insert categories using wrangler d1 execute
  }

  /**
   * Insert posts into D1
   */
  async insertPosts() {
    console.log('📝 Inserting posts...');
    // Implementation would insert posts using wrangler d1 execute
  }

  /**
   * Execute SQL command in D1 database
   */
  async executeD1Command(sql) {
    const command = `wrangler d1 execute ${CONFIG.DATABASE_NAME} --local --command="${sql}"`;
    
    try {
      const result = execSync(command, { encoding: 'utf8' });
      return result;
    } catch (error) {
      console.error('D1 command failed:', error.message);
      throw error;
    }
  }

  /**
   * Print migration summary
   */
  printSummary() {
    console.log('\n📊 Migration Summary:');
    console.log(`Posts: ${this.extractedData.posts.length}`);
    console.log(`Users: ${this.extractedData.users.length}`);
    console.log(`Categories: ${this.extractedData.categories.length}`);
    console.log(`Tags: ${this.extractedData.tags.length}`);
  }
}

// Run migration if this file is executed directly
if (require.main === module) {
  const migrator = new WordPressMigrator();
  migrator.migrate().catch(console.error);
}

module.exports = WordPressMigrator;
