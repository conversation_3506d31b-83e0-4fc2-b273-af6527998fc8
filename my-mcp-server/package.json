{"name": "remote-mcp-server-authless", "version": "0.0.0", "private": true, "scripts": {"deploy": "wrangler deploy", "dev": "wrangler dev", "format": "biome format --write", "lint:fix": "biome lint --fix", "start": "wrangler dev", "cf-typegen": "wrangler types", "type-check": "tsc --noEmit"}, "dependencies": {"@modelcontextprotocol/sdk": "1.13.1", "agents": "^0.0.100", "zod": "^3.25.67"}, "devDependencies": {"@biomejs/biome": "^2.0.6", "@types/node": "^24.1.0", "typescript": "^5.8.3", "wrangler": "^4.26.1"}}