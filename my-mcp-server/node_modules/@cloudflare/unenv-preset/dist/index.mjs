

// -- Unbuild CommonJS Shims --
import __cjs_url__ from 'url';
import __cjs_path__ from 'path';
import __cjs_mod__ from 'module';
const __filename = __cjs_url__.fileURLToPath(import.meta.url);
const __dirname = __cjs_path__.dirname(__filename);
const require = __cjs_mod__.createRequire(import.meta.url);
const version = "2.5.0";

const nodeCompatModules = [
  "_stream_duplex",
  "_stream_passthrough",
  "_stream_readable",
  "_stream_transform",
  "_stream_writable",
  "_tls_common",
  "_tls_wrap",
  "assert",
  "assert/strict",
  "buffer",
  "diagnostics_channel",
  "dns",
  "dns/promises",
  "events",
  "net",
  "path",
  "path/posix",
  "path/win32",
  "querystring",
  "stream",
  "stream/consumers",
  "stream/promises",
  "stream/web",
  "string_decoder",
  "timers",
  "timers/promises",
  "url",
  "util/types",
  "zlib"
];
const hybridNodeCompatModules = [
  "async_hooks",
  "console",
  "crypto",
  "module",
  "process",
  "tls",
  "util"
];
function getCloudflarePreset({
  compatibilityDate = "2024-09-03",
  compatibilityFlags = []
}) {
  return {
    meta: {
      name: "unenv:cloudflare",
      version,
      url: __filename
    },
    alias: {
      // `nodeCompatModules` are implemented in workerd.
      // Create aliases to override polyfills defined in based environments.
      ...Object.fromEntries(
        nodeCompatModules.flatMap((p) => [
          [p, p],
          [`node:${p}`, `node:${p}`]
        ])
      ),
      // The `node:sys` module is just a deprecated alias for `node:util` which we implemented using a hybrid polyfill
      sys: "@cloudflare/unenv-preset/node/util",
      "node:sys": "@cloudflare/unenv-preset/node/util",
      // `hybridNodeCompatModules` are implemented by the cloudflare preset.
      ...Object.fromEntries(
        hybridNodeCompatModules.flatMap((m) => [
          [m, `@cloudflare/unenv-preset/node/${m}`],
          [`node:${m}`, `@cloudflare/unenv-preset/node/${m}`]
        ])
      ),
      // Use either the unenv or native implementation
      ...getHttpAliases({ compatibilityDate, compatibilityFlags }),
      // To override the npm shim from unenv
      debug: "@cloudflare/unenv-preset/npm/debug"
    },
    inject: {
      // Setting symbols implemented by workerd to `false` so that `inject`s defined in base presets are not used.
      Buffer: false,
      global: false,
      clearImmediate: false,
      setImmediate: false,
      console: "@cloudflare/unenv-preset/node/console",
      process: "@cloudflare/unenv-preset/node/process"
    },
    polyfill: ["@cloudflare/unenv-preset/polyfill/performance"],
    external: nodeCompatModules.flatMap((p) => [p, `node:${p}`])
  };
}
function getHttpAliases({
  compatibilityDate,
  compatibilityFlags
}) {
  const disabledByFlag = compatibilityFlags.includes(
    "disable_nodejs_http_modules"
  );
  const enabledByFlags = compatibilityFlags.includes(
    "enable_nodejs_http_modules"
  );
  const enabledByDate = compatibilityDate >= "2025-08-15";
  const enabled = (enabledByFlags || enabledByDate) && !disabledByFlag;
  if (!enabled) {
    return {};
  }
  const aliases = {};
  const nativeModules = [
    "_http_common",
    "_http_outgoing",
    "_http_client",
    "_http_incoming",
    "_http_agent"
  ];
  for (const nativeModule of nativeModules) {
    aliases[nativeModule] = nativeModule;
    aliases[`node:${nativeModule}`] = `node:${nativeModule}`;
  }
  const hybridModules = ["http", "https"];
  for (const hybridModule of hybridModules) {
    aliases[hybridModule] = `@cloudflare/unenv-preset/node/${hybridModule}`;
    aliases[`node:${hybridModule}`] = `@cloudflare/unenv-preset/node/${hybridModule}`;
  }
  return aliases;
}

const cloudflare = getCloudflarePreset({});

export { cloudflare, getCloudflarePreset };
