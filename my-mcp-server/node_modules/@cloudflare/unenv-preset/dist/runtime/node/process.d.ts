export declare const getBuiltinModule: NodeJS.Process["getBuiltinModule"];
export declare const exit: {
    (code?: number | string | null | undefined): never;
    (code?: number | string | null | undefined): never;
}, platform: NodeJS.Platform, nextTick: {
    (callback: Function, ...args: any[]): void;
    (callback: Function, ...args: any[]): void;
};
export declare const abort: any, addListener: any, allowedNodeEnvironmentFlags: any, hasUncaughtExceptionCaptureCallback: any, setUncaughtExceptionCaptureCallback: any, loadEnvFile: any, sourceMapsEnabled: any, arch: any, argv: any, argv0: any, chdir: any, config: any, connected: any, constrainedMemory: any, availableMemory: any, cpuUsage: any, cwd: any, debugPort: any, dlopen: any, disconnect: any, emit: any, emitWarning: any, env: any, eventNames: any, execArgv: any, execPath: any, finalization: any, features: any, getActiveResourcesInfo: any, getMaxListeners: any, hrtime: any, kill: any, listeners: any, listenerCount: any, memoryUsage: any, on: any, off: any, once: any, pid: any, ppid: any, prependListener: any, prependOnceListener: any, rawListeners: any, release: any, removeAllListeners: any, removeListener: any, report: any, resourceUsage: any, setMaxListeners: any, setSourceMapsEnabled: any, stderr: any, stdin: any, stdout: any, title: any, throwDeprecation: any, traceDeprecation: any, umask: any, uptime: any, version: any, versions: any, domain: any, initgroups: any, moduleLoadList: any, reallyExit: any, openStdin: any, assert: any, binding: any, send: any, exitCode: any, channel: any, getegid: any, geteuid: any, getgid: any, getgroups: any, getuid: any, setegid: any, seteuid: any, setgid: any, setgroups: any, setuid: any, permission: any, mainModule: any, _events: any, _eventsCount: any, _exiting: any, _maxListeners: any, _debugEnd: any, _debugProcess: any, _fatalException: any, _getActiveHandles: any, _getActiveRequests: any, _kill: any, _preload_modules: any, _rawDebug: any, _startProfilerIdleNotifier: any, _stopProfilerIdleNotifier: any, _tickCallback: any, _disconnect: any, _handleQueue: any, _pendingMessage: any, _channel: any, _send: any, _linkedBinding: any;
declare const _default: NodeJS.Process;
export default _default;
