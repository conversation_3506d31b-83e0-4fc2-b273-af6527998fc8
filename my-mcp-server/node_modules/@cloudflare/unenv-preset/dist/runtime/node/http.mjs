import {
  _connectionListener,
  CloseEvent,
  createServer,
  maxHeaderSize,
  MessageEvent,
  Server,
  ServerResponse,
  setMaxIdleHTTPParsers,
  WebSocket
} from "unenv/node/http";
export {
  _connectionListener,
  CloseEvent,
  createServer,
  maxHeaderSize,
  MessageEvent,
  Server,
  ServerResponse,
  setMaxIdleHTTPParsers,
  WebSocket
} from "unenv/node/http";
const workerdHttp = process.getBuiltinModule("node:http");
export const {
  Agent,
  ClientRequest,
  globalAgent,
  IncomingMessage,
  METHODS,
  OutgoingMessage,
  STATUS_CODES,
  validateHeaderName,
  validateHeaderValue,
  request,
  get
} = workerdHttp;
export default {
  _connectionListener,
  Agent,
  ClientRequest,
  // @ts-expect-error Node types do not match unenv
  CloseEvent,
  // @ts-expect-error Node types do not match unenv
  createServer,
  get,
  globalAgent,
  IncomingMessage,
  maxHeaderSize,
  // @ts-expect-error Node types do not match unenv
  MessageEvent,
  METHODS,
  OutgoingMessage,
  request,
  Server,
  // @ts-expect-error Node types do not match unenv
  ServerResponse,
  // @ts-expect-error Node types do not match unenv
  setMaxIdleHTTPParsers,
  STATUS_CODES,
  validateHeaderName,
  validateHeaderValue,
  // @ts-expect-error Node types do not match unenv
  WebSocket
};
