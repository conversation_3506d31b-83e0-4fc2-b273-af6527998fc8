export { _connectionL<PERSON><PERSON>, CloseEvent, createServer, maxHeaderSize, MessageEvent, Server, ServerResponse, setMaxIdleHTTPParsers, WebSocket, } from "unenv/node/http";
export declare const Agent: typeof import("http").Agent, ClientRequest: typeof import("http").ClientRequest, globalAgent: import("http").Agent, IncomingMessage: typeof import("http").IncomingMessage, METHODS: string[], OutgoingMessage: typeof import("http").OutgoingMessage, STATUS_CODES: {
    [errorCode: number]: string | undefined;
    [errorCode: string]: string | undefined;
}, validateHeaderName: typeof import("http").validateHeaderName, validateHeaderValue: typeof import("http").validateHeaderValue, request: typeof import("http").request, get: typeof import("http").get;
declare const _default: any;
export default _default;
