var m=Object.defineProperty;var p=Object.getOwnPropertyDescriptor;var s=Object.getOwnPropertyNames;var u=Object.prototype.hasOwnProperty;var b=(e,t)=>{for(var a in t)m(e,a,{get:t[a],enumerable:!0})},c=(e,t,a,n)=>{if(t&&typeof t=="object"||typeof t=="function")for(let r of s(t))!u.call(e,r)&&r!==a&&m(e,r,{get:()=>t[r],enumerable:!(n=p(t,r))||n.enumerable});return e};var d=e=>c(m({},"__esModule",{value:!0}),e);var h={};b(h,{default:()=>g});module.exports=d(h);var g={num:{type:"num",match:/(\.e?|\b)\d(e-|[\d.oxa-fA-F_])*(\.|\b)/g},str:{type:"str",match:/(["'])(\\[^]|(?!\1)[^\r\n\\])*\1?/g},strDouble:{type:"str",match:/"((?!")[^\r\n\\]|\\[^])*"?/g}};
