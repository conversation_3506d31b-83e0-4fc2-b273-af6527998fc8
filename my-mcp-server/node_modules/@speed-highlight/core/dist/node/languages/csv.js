var o=Object.defineProperty;var d=Object.getOwnPropertyDescriptor;var l=Object.getOwnPropertyNames;var u=Object.prototype.hasOwnProperty;var x=(t,e)=>{for(var a in e)o(t,a,{get:e[a],enumerable:!0})},b=(t,e,a,r)=>{if(e&&typeof e=="object"||typeof e=="function")for(let p of l(e))!u.call(t,p)&&p!==a&&o(t,p,{get:()=>e[p],enumerable:!(r=d(e,p))||r.enumerable});return t};var c=t=>b(o({},"__esModule",{value:!0}),t);var g={};x(g,{default:()=>f});module.exports=c(g);var f=[{expand:"strDouble"},{type:"oper",match:/,/g}];
