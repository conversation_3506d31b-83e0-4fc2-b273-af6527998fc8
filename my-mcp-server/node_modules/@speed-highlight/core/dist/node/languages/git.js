var c=Object.defineProperty;var g=Object.getOwnPropertyDescriptor;var o=Object.getOwnPropertyNames;var h=Object.prototype.hasOwnProperty;var f=(m,t)=>{for(var a in t)c(m,a,{get:t[a],enumerable:!0})},i=(m,t,a,d)=>{if(t&&typeof t=="object"||typeof t=="function")for(let e of o(t))!h.call(m,e)&&e!==a&&c(m,e,{get:()=>t[e],enumerable:!(d=g(t,e))||d.enumerable});return m};var r=m=>i(c({},"__esModule",{value:!0}),m);var s={};f(s,{default:()=>y});module.exports=r(s);var p=[{type:"deleted",match:/^[-<].*/gm},{type:"insert",match:/^[+>].*/gm},{type:"kwd",match:/!.*/gm},{type:"section",match:/^@@.*@@$|^\d.*|^([*-+])\1\1.*/gm}];var y=[{match:/^#.*/gm,sub:"todo"},{expand:"str"},...p,{type:"func",match:/^(\$ )?git(\s.*)?$/gm},{type:"kwd",match:/^commit \w+$/gm}];
