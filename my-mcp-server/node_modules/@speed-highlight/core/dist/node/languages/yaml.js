var m=Object.defineProperty;var r=Object.getOwnPropertyDescriptor;var c=Object.getOwnPropertyNames;var g=Object.prototype.hasOwnProperty;var h=(a,t)=>{for(var p in t)m(a,p,{get:t[p],enumerable:!0})},n=(a,t,p,o)=>{if(t&&typeof t=="object"||typeof t=="function")for(let e of c(t))!g.call(a,e)&&e!==p&&m(a,e,{get:()=>t[e],enumerable:!(o=r(t,e))||o.enumerable});return a};var y=a=>n(m({},"__esModule",{value:!0}),a);var b={};h(b,{default:()=>s});module.exports=y(b);var s=[{match:/#.*/g,sub:"todo"},{expand:"str"},{type:"str",match:/(>|\|)\r?\n((\s[^\n]*)?(\r?\n|$))*/g},{type:"type",match:/!![a-z]+/g},{type:"bool",match:/\b(Yes|No)\b/g},{type:"oper",match:/[+:-]/g},{expand:"num"},{type:"var",match:/[a-zA-Z]\w*(?=:)/g}];
