var r=Object.defineProperty;var o=Object.getOwnPropertyDescriptor;var p=Object.getOwnPropertyNames;var s=Object.prototype.hasOwnProperty;var g=(e,t)=>{for(var c in t)r(e,c,{get:t[c],enumerable:!0})},u=(e,t,c,n)=>{if(t&&typeof t=="object"||typeof t=="function")for(let a of p(t))!s.call(e,a)&&a!==c&&r(e,a,{get:()=>t[a],enumerable:!(n=o(t,a))||n.enumerable});return e};var f=e=>u(r({},"__esModule",{value:!0}),e);var m={};g(m,{default:()=>h});module.exports=f(m);var h=[{match:/\/\/.*\n?|\/\*((?!\*\/)[^])*(\*\/)?/g,sub:"todo"},{expand:"str"},{expand:"num"},{type:"kwd",match:/\*|&|\b(break|case|chan|const|continue|default|defer|else|fallthrough|for|func|go|goto|if|import|interface|map|package|range|return|select|struct|switch|type|var)\b/g},{type:"func",match:/[a-zA-Z_][\w_]*(?=\s*\()/g},{type:"class",match:/\b[A-Z][\w_]*\b/g},{type:"oper",match:/[+\-*\/%&|^~=!<>.^-]+/g}];
