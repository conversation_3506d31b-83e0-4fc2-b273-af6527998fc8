declare const _default: {
    match: {
        exec(str: any): {
            index: number;
            0: any;
        };
        lastIndex: any;
    };
    sub: ({
        type: string;
        match: RegExp;
        sub?: undefined;
    } | {
        match: RegExp;
        sub: string;
        type?: undefined;
    })[];
}[];
export default _default;
export let type: string;
//# sourceMappingURL=js_template_literals.d.ts.map