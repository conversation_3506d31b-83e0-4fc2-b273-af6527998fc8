{"clientTcpRtt": 10, "requestHeaderNames": {}, "httpProtocol": "HTTP/1.1", "tlsCipher": "AEAD-AES256-GCM-SHA384", "continent": "AS", "asn": 133661, "clientAcceptEncoding": "gzip, deflate, br", "verifiedBotCategory": "", "country": "IN", "isEUCountry": false, "region": "Punjab", "tlsClientCiphersSha1": "kXrN3VEKDdzz2cPKTQaKzpxVTxQ=", "tlsClientAuth": {"certIssuerDNLegacy": "", "certIssuerSKI": "", "certSubjectDNRFC2253": "", "certSubjectDNLegacy": "", "certFingerprintSHA256": "", "certNotBefore": "", "certSKI": "", "certSerial": "", "certIssuerDN": "", "certVerified": "NONE", "certNotAfter": "", "certSubjectDN": "", "certPresented": "0", "certRevoked": "0", "certIssuerSerial": "", "certIssuerDNRFC2253": "", "certFingerprintSHA1": ""}, "tlsClientRandom": "mQFZbAIWo70FcRSMgOH3gX9w7piQKQX1vCCT0uk55yQ=", "tlsExportedAuthenticator": {"clientFinished": "583db99c157254c475efcfd41f00641a732a4da3eda7e537da966952a6a8f71e333bac5784c742e28c07fed24e97c64c", "clientHandshake": "e57e8bb852eabb3f4f16ea78cb1324121db49ee746bf17a842b71513d1a200d447e5b8e8db6eb1f830695ae0747d9283", "serverHandshake": "1a7b59511c98079f92c478af8ef3e6b7baece9ea1c166bcd3f7a20677e6e1ad7750a0e8ea980263ed835362e16e033d7", "serverFinished": "d8546ed2782344746fb392504db1af6c018423546b0b3b4041a2c82430d235807b12e1d925719e9ebb385dc367ba17b7"}, "tlsClientHelloLength": "1605", "colo": "DEL", "timezone": "Asia/Kolkata", "longitude": "75.85379", "latitude": "30.91204", "edgeRequestKeepAliveStatus": 1, "requestPriority": "", "postalCode": "141120", "city": "<PERSON><PERSON><PERSON><PERSON>", "tlsVersion": "TLSv1.3", "regionCode": "PB", "asOrganization": "Netplus Broadband Services Private Limited", "tlsClientExtensionsSha1Le": "u4wtEMFQBY18l3BzHAvORm+KGRw=", "tlsClientExtensionsSha1": "1eY97BUYYO8vDaTfHQywB1pcNdM=", "botManagement": {"corporateProxy": false, "verifiedBot": false, "jsDetection": {"passed": false}, "staticResource": false, "detectionIds": {}, "score": 99}}