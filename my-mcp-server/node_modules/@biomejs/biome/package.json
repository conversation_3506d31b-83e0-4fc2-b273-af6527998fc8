{"name": "@biomejs/biome", "version": "2.0.6", "bin": {"biome": "bin/biome"}, "homepage": "https://biomejs.dev", "repository": {"type": "git", "url": "git+https://github.com/biomejs/biome.git", "directory": "packages/@biomejs/biome"}, "author": "<PERSON><PERSON>", "license": "MIT OR Apache-2.0", "bugs": "https://github.com/biomejs/biome/issues", "description": "Biome is a toolchain for the web: formatter, linter and more", "files": ["bin/biome", "configuration_schema.json", "README.md", "LICENSE-APACHE", "LICENSE-MIT", "ROME-LICENSE-MIT"], "keywords": ["format", "lint", "toolchain", "JavaScript", "TypeScript", "JSON", "JSONC", "JSX", "TSX", "CSS", "GraphQL"], "engines": {"node": ">=14.21.3"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/biome"}, "publishConfig": {"provenance": true}, "optionalDependencies": {"@biomejs/cli-win32-x64": "2.0.6", "@biomejs/cli-win32-arm64": "2.0.6", "@biomejs/cli-darwin-x64": "2.0.6", "@biomejs/cli-darwin-arm64": "2.0.6", "@biomejs/cli-linux-x64": "2.0.6", "@biomejs/cli-linux-arm64": "2.0.6", "@biomejs/cli-linux-x64-musl": "2.0.6", "@biomejs/cli-linux-arm64-musl": "2.0.6"}}