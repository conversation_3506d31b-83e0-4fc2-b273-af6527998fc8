{"version": 3, "sources": ["../src/index.ts", "../src/assistant-stream-parts.ts", "../src/process-chat-response.ts", "../src/duplicated/usage.ts", "../src/parse-partial-json.ts", "../src/fix-json.ts", "../src/data-stream-parts.ts", "../src/process-data-stream.ts", "../src/process-chat-text-response.ts", "../src/process-text-stream.ts", "../src/call-chat-api.ts", "../src/call-completion-api.ts", "../src/data-url.ts", "../src/extract-max-tool-invocation-step.ts", "../src/get-message-parts.ts", "../src/fill-message-parts.ts", "../src/is-deep-equal-data.ts", "../src/prepare-attachments-for-request.ts", "../src/process-assistant-stream.ts", "../src/schema.ts", "../src/zod-schema.ts", "../src/should-resubmit-messages.ts", "../src/update-tool-call-result.ts"], "sourcesContent": ["export * from './types';\n\nexport { generateId } from '@ai-sdk/provider-utils';\n\n// Export stream data utilities for custom stream implementations,\n// both on the client and server side.\n// NOTE: this is experimental / internal and may change without notice\nexport {\n  formatAssistantStreamPart,\n  parseAssistantStreamPart,\n} from './assistant-stream-parts';\nexport type {\n  AssistantStreamPart,\n  AssistantStreamString,\n} from './assistant-stream-parts';\nexport { callChatApi } from './call-chat-api';\nexport { callCompletionApi } from './call-completion-api';\nexport { formatDataStreamPart, parseDataStreamPart } from './data-stream-parts';\nexport type { DataStreamPart, DataStreamString } from './data-stream-parts';\nexport { getTextFromDataUrl } from './data-url';\nexport type { DeepPartial } from './deep-partial';\nexport { extractMaxToolInvocationStep } from './extract-max-tool-invocation-step';\nexport { fillMessageParts } from './fill-message-parts';\nexport { getMessageParts } from './get-message-parts';\nexport { isDeepEqualData } from './is-deep-equal-data';\nexport { parsePartialJson } from './parse-partial-json';\nexport { prepareAttachmentsForRequest } from './prepare-attachments-for-request';\nexport { processAssistantStream } from './process-assistant-stream';\nexport { processDataStream } from './process-data-stream';\nexport { processTextStream } from './process-text-stream';\nexport { asSchema, jsonSchema } from './schema';\nexport type { Schema } from './schema';\nexport {\n  isAssistantMessageWithCompletedToolCalls,\n  shouldResubmitMessages,\n} from './should-resubmit-messages';\nexport { updateToolCallResult } from './update-tool-call-result';\nexport { zodSchema } from './zod-schema';\n", "import { AssistantMessage, DataMessage, JSONValue } from './types';\n\nexport type AssistantStreamString =\n  `${(typeof StreamStringPrefixes)[keyof typeof StreamStringPrefixes]}:${string}\\n`;\n\nexport interface AssistantStreamPart<\n  CODE extends string,\n  NAME extends string,\n  TYPE,\n> {\n  code: CODE;\n  name: NAME;\n  parse: (value: JSONValue) => { type: NAME; value: TYPE };\n}\n\nconst textStreamPart: AssistantStreamPart<'0', 'text', string> = {\n  code: '0',\n  name: 'text',\n  parse: (value: JSONValue) => {\n    if (typeof value !== 'string') {\n      throw new Error('\"text\" parts expect a string value.');\n    }\n    return { type: 'text', value };\n  },\n};\n\nconst errorStreamPart: AssistantStreamPart<'3', 'error', string> = {\n  code: '3',\n  name: 'error',\n  parse: (value: JSONValue) => {\n    if (typeof value !== 'string') {\n      throw new Error('\"error\" parts expect a string value.');\n    }\n    return { type: 'error', value };\n  },\n};\n\nconst assistantMessageStreamPart: AssistantStreamPart<\n  '4',\n  'assistant_message',\n  AssistantMessage\n> = {\n  code: '4',\n  name: 'assistant_message',\n  parse: (value: JSONValue) => {\n    if (\n      value == null ||\n      typeof value !== 'object' ||\n      !('id' in value) ||\n      !('role' in value) ||\n      !('content' in value) ||\n      typeof value.id !== 'string' ||\n      typeof value.role !== 'string' ||\n      value.role !== 'assistant' ||\n      !Array.isArray(value.content) ||\n      !value.content.every(\n        item =>\n          item != null &&\n          typeof item === 'object' &&\n          'type' in item &&\n          item.type === 'text' &&\n          'text' in item &&\n          item.text != null &&\n          typeof item.text === 'object' &&\n          'value' in item.text &&\n          typeof item.text.value === 'string',\n      )\n    ) {\n      throw new Error(\n        '\"assistant_message\" parts expect an object with an \"id\", \"role\", and \"content\" property.',\n      );\n    }\n\n    return {\n      type: 'assistant_message',\n      value: value as AssistantMessage,\n    };\n  },\n};\n\nconst assistantControlDataStreamPart: AssistantStreamPart<\n  '5',\n  'assistant_control_data',\n  {\n    threadId: string;\n    messageId: string;\n  }\n> = {\n  code: '5',\n  name: 'assistant_control_data',\n  parse: (value: JSONValue) => {\n    if (\n      value == null ||\n      typeof value !== 'object' ||\n      !('threadId' in value) ||\n      !('messageId' in value) ||\n      typeof value.threadId !== 'string' ||\n      typeof value.messageId !== 'string'\n    ) {\n      throw new Error(\n        '\"assistant_control_data\" parts expect an object with a \"threadId\" and \"messageId\" property.',\n      );\n    }\n\n    return {\n      type: 'assistant_control_data',\n      value: {\n        threadId: value.threadId,\n        messageId: value.messageId,\n      },\n    };\n  },\n};\n\nconst dataMessageStreamPart: AssistantStreamPart<\n  '6',\n  'data_message',\n  DataMessage\n> = {\n  code: '6',\n  name: 'data_message',\n  parse: (value: JSONValue) => {\n    if (\n      value == null ||\n      typeof value !== 'object' ||\n      !('role' in value) ||\n      !('data' in value) ||\n      typeof value.role !== 'string' ||\n      value.role !== 'data'\n    ) {\n      throw new Error(\n        '\"data_message\" parts expect an object with a \"role\" and \"data\" property.',\n      );\n    }\n\n    return {\n      type: 'data_message',\n      value: value as DataMessage,\n    };\n  },\n};\n\nconst assistantStreamParts = [\n  textStreamPart,\n  errorStreamPart,\n  assistantMessageStreamPart,\n  assistantControlDataStreamPart,\n  dataMessageStreamPart,\n] as const;\n\ntype AssistantStreamParts =\n  | typeof textStreamPart\n  | typeof errorStreamPart\n  | typeof assistantMessageStreamPart\n  | typeof assistantControlDataStreamPart\n  | typeof dataMessageStreamPart;\n\ntype AssistantStreamPartValueType = {\n  [P in AssistantStreamParts as P['name']]: ReturnType<P['parse']>['value'];\n};\n\nexport type AssistantStreamPartType =\n  | ReturnType<typeof textStreamPart.parse>\n  | ReturnType<typeof errorStreamPart.parse>\n  | ReturnType<typeof assistantMessageStreamPart.parse>\n  | ReturnType<typeof assistantControlDataStreamPart.parse>\n  | ReturnType<typeof dataMessageStreamPart.parse>;\n\nexport const assistantStreamPartsByCode = {\n  [textStreamPart.code]: textStreamPart,\n  [errorStreamPart.code]: errorStreamPart,\n  [assistantMessageStreamPart.code]: assistantMessageStreamPart,\n  [assistantControlDataStreamPart.code]: assistantControlDataStreamPart,\n  [dataMessageStreamPart.code]: dataMessageStreamPart,\n} as const;\n\nexport const StreamStringPrefixes = {\n  [textStreamPart.name]: textStreamPart.code,\n  [errorStreamPart.name]: errorStreamPart.code,\n  [assistantMessageStreamPart.name]: assistantMessageStreamPart.code,\n  [assistantControlDataStreamPart.name]: assistantControlDataStreamPart.code,\n  [dataMessageStreamPart.name]: dataMessageStreamPart.code,\n} as const;\n\nexport const validCodes = assistantStreamParts.map(part => part.code);\n\nexport const parseAssistantStreamPart = (\n  line: string,\n): AssistantStreamPartType => {\n  const firstSeparatorIndex = line.indexOf(':');\n\n  if (firstSeparatorIndex === -1) {\n    throw new Error('Failed to parse stream string. No separator found.');\n  }\n\n  const prefix = line.slice(0, firstSeparatorIndex);\n\n  if (!validCodes.includes(prefix as keyof typeof assistantStreamPartsByCode)) {\n    throw new Error(`Failed to parse stream string. Invalid code ${prefix}.`);\n  }\n\n  const code = prefix as keyof typeof assistantStreamPartsByCode;\n\n  const textValue = line.slice(firstSeparatorIndex + 1);\n  const jsonValue: JSONValue = JSON.parse(textValue);\n\n  return assistantStreamPartsByCode[code].parse(jsonValue);\n};\n\nexport function formatAssistantStreamPart<\n  T extends keyof AssistantStreamPartValueType,\n>(type: T, value: AssistantStreamPartValueType[T]): AssistantStreamString {\n  const streamPart = assistantStreamParts.find(part => part.name === type);\n\n  if (!streamPart) {\n    throw new Error(`Invalid stream part type: ${type}`);\n  }\n\n  return `${streamPart.code}:${JSON.stringify(value)}\\n`;\n}\n", "import { LanguageModelV1FinishReason } from '@ai-sdk/provider';\nimport { generateId as generateIdFunction } from '@ai-sdk/provider-utils';\nimport {\n  calculateLanguageModelUsage,\n  LanguageModelUsage,\n} from './duplicated/usage';\nimport { parsePartialJson } from './parse-partial-json';\nimport { processDataStream } from './process-data-stream';\nimport type {\n  JSONValue,\n  ReasoningUIPart,\n  TextUIPart,\n  ToolInvocation,\n  ToolInvocationUIPart,\n  UIMessage,\n  UseChatOptions,\n} from './types';\n\nexport async function processChatResponse({\n  stream,\n  update,\n  onToolCall,\n  onFinish,\n  generateId = generateIdFunction,\n  getCurrentDate = () => new Date(),\n  lastMessage,\n}: {\n  stream: ReadableStream<Uint8Array>;\n  update: (options: {\n    message: UIMessage;\n    data: JSONValue[] | undefined;\n    replaceLastMessage: boolean;\n  }) => void;\n  onToolCall?: UseChatOptions['onToolCall'];\n  onFinish?: (options: {\n    message: UIMessage | undefined;\n    finishReason: LanguageModelV1FinishReason;\n    usage: LanguageModelUsage;\n  }) => void;\n  generateId?: () => string;\n  getCurrentDate?: () => Date;\n  lastMessage: UIMessage | undefined;\n}) {\n  const replaceLastMessage = lastMessage?.role === 'assistant';\n  let step = replaceLastMessage\n    ? 1 +\n      // find max step in existing tool invocations:\n      (lastMessage.toolInvocations?.reduce((max, toolInvocation) => {\n        return Math.max(max, toolInvocation.step ?? 0);\n      }, 0) ?? 0)\n    : 0;\n\n  const message: UIMessage = replaceLastMessage\n    ? structuredClone(lastMessage)\n    : {\n        id: generateId(),\n        createdAt: getCurrentDate(),\n        role: 'assistant',\n        content: '',\n        parts: [],\n      };\n\n  let currentTextPart: TextUIPart | undefined = undefined;\n  let currentReasoningPart: ReasoningUIPart | undefined = undefined;\n  let currentReasoningTextDetail:\n    | { type: 'text'; text: string; signature?: string }\n    | undefined = undefined;\n\n  function updateToolInvocationPart(\n    toolCallId: string,\n    invocation: ToolInvocation,\n  ) {\n    const part = message.parts.find(\n      part =>\n        part.type === 'tool-invocation' &&\n        part.toolInvocation.toolCallId === toolCallId,\n    ) as ToolInvocationUIPart | undefined;\n\n    if (part != null) {\n      part.toolInvocation = invocation;\n    } else {\n      message.parts.push({\n        type: 'tool-invocation',\n        toolInvocation: invocation,\n      });\n    }\n  }\n\n  const data: JSONValue[] = [];\n\n  // keep list of current message annotations for message\n  let messageAnnotations: JSONValue[] | undefined = replaceLastMessage\n    ? lastMessage?.annotations\n    : undefined;\n\n  // keep track of partial tool calls\n  const partialToolCalls: Record<\n    string,\n    { text: string; step: number; index: number; toolName: string }\n  > = {};\n\n  let usage: LanguageModelUsage = {\n    completionTokens: NaN,\n    promptTokens: NaN,\n    totalTokens: NaN,\n  };\n  let finishReason: LanguageModelV1FinishReason = 'unknown';\n\n  function execUpdate() {\n    // make a copy of the data array to ensure UI is updated (SWR)\n    const copiedData = [...data];\n\n    // keeps the currentMessage up to date with the latest annotations,\n    // even if annotations preceded the message creation\n    if (messageAnnotations?.length) {\n      message.annotations = messageAnnotations;\n    }\n\n    const copiedMessage = {\n      // deep copy the message to ensure that deep changes (msg attachments) are updated\n      // with SolidJS. SolidJS uses referential integration of sub-objects to detect changes.\n      ...structuredClone(message),\n      // add a revision id to ensure that the message is updated with SWR. SWR uses a\n      // hashing approach by default to detect changes, but it only works for shallow\n      // changes. This is why we need to add a revision id to ensure that the message\n      // is updated with SWR (without it, the changes get stuck in SWR and are not\n      // forwarded to rendering):\n      revisionId: generateId(),\n    } as UIMessage;\n\n    update({\n      message: copiedMessage,\n      data: copiedData,\n      replaceLastMessage,\n    });\n  }\n\n  await processDataStream({\n    stream,\n    onTextPart(value) {\n      if (currentTextPart == null) {\n        currentTextPart = {\n          type: 'text',\n          text: value,\n        };\n        message.parts.push(currentTextPart);\n      } else {\n        currentTextPart.text += value;\n      }\n\n      message.content += value;\n      execUpdate();\n    },\n    onReasoningPart(value) {\n      if (currentReasoningTextDetail == null) {\n        currentReasoningTextDetail = { type: 'text', text: value };\n        if (currentReasoningPart != null) {\n          currentReasoningPart.details.push(currentReasoningTextDetail);\n        }\n      } else {\n        currentReasoningTextDetail.text += value;\n      }\n\n      if (currentReasoningPart == null) {\n        currentReasoningPart = {\n          type: 'reasoning',\n          reasoning: value,\n          details: [currentReasoningTextDetail],\n        };\n        message.parts.push(currentReasoningPart);\n      } else {\n        currentReasoningPart.reasoning += value;\n      }\n\n      message.reasoning = (message.reasoning ?? '') + value;\n\n      execUpdate();\n    },\n    onReasoningSignaturePart(value) {\n      if (currentReasoningTextDetail != null) {\n        currentReasoningTextDetail.signature = value.signature;\n      }\n    },\n    onRedactedReasoningPart(value) {\n      if (currentReasoningPart == null) {\n        currentReasoningPart = {\n          type: 'reasoning',\n          reasoning: '',\n          details: [],\n        };\n        message.parts.push(currentReasoningPart);\n      }\n\n      currentReasoningPart.details.push({\n        type: 'redacted',\n        data: value.data,\n      });\n\n      currentReasoningTextDetail = undefined;\n\n      execUpdate();\n    },\n    onFilePart(value) {\n      message.parts.push({\n        type: 'file',\n        mimeType: value.mimeType,\n        data: value.data,\n      });\n\n      execUpdate();\n    },\n    onSourcePart(value) {\n      message.parts.push({\n        type: 'source',\n        source: value,\n      });\n\n      execUpdate();\n    },\n    onToolCallStreamingStartPart(value) {\n      if (message.toolInvocations == null) {\n        message.toolInvocations = [];\n      }\n\n      // add the partial tool call to the map\n      partialToolCalls[value.toolCallId] = {\n        text: '',\n        step,\n        toolName: value.toolName,\n        index: message.toolInvocations.length,\n      };\n\n      const invocation = {\n        state: 'partial-call',\n        step,\n        toolCallId: value.toolCallId,\n        toolName: value.toolName,\n        args: undefined,\n      } as const;\n\n      message.toolInvocations.push(invocation);\n\n      updateToolInvocationPart(value.toolCallId, invocation);\n\n      execUpdate();\n    },\n    onToolCallDeltaPart(value) {\n      const partialToolCall = partialToolCalls[value.toolCallId];\n\n      partialToolCall.text += value.argsTextDelta;\n\n      const { value: partialArgs } = parsePartialJson(partialToolCall.text);\n\n      const invocation = {\n        state: 'partial-call',\n        step: partialToolCall.step,\n        toolCallId: value.toolCallId,\n        toolName: partialToolCall.toolName,\n        args: partialArgs,\n      } as const;\n\n      message.toolInvocations![partialToolCall.index] = invocation;\n\n      updateToolInvocationPart(value.toolCallId, invocation);\n\n      execUpdate();\n    },\n    async onToolCallPart(value) {\n      const invocation = {\n        state: 'call',\n        step,\n        ...value,\n      } as const;\n\n      if (partialToolCalls[value.toolCallId] != null) {\n        // change the partial tool call to a full tool call\n        message.toolInvocations![partialToolCalls[value.toolCallId].index] =\n          invocation;\n      } else {\n        if (message.toolInvocations == null) {\n          message.toolInvocations = [];\n        }\n\n        message.toolInvocations.push(invocation);\n      }\n\n      updateToolInvocationPart(value.toolCallId, invocation);\n\n      execUpdate();\n\n      // invoke the onToolCall callback if it exists. This is blocking.\n      // In the future we should make this non-blocking, which\n      // requires additional state management for error handling etc.\n      if (onToolCall) {\n        const result = await onToolCall({ toolCall: value });\n        if (result != null) {\n          const invocation = {\n            state: 'result',\n            step,\n            ...value,\n            result,\n          } as const;\n\n          // store the result in the tool invocation\n          message.toolInvocations![message.toolInvocations!.length - 1] =\n            invocation;\n\n          updateToolInvocationPart(value.toolCallId, invocation);\n\n          execUpdate();\n        }\n      }\n    },\n    onToolResultPart(value) {\n      const toolInvocations = message.toolInvocations;\n\n      if (toolInvocations == null) {\n        throw new Error('tool_result must be preceded by a tool_call');\n      }\n\n      // find if there is any tool invocation with the same toolCallId\n      // and replace it with the result\n      const toolInvocationIndex = toolInvocations.findIndex(\n        invocation => invocation.toolCallId === value.toolCallId,\n      );\n\n      if (toolInvocationIndex === -1) {\n        throw new Error(\n          'tool_result must be preceded by a tool_call with the same toolCallId',\n        );\n      }\n\n      const invocation = {\n        ...toolInvocations[toolInvocationIndex],\n        state: 'result' as const,\n        ...value,\n      } as const;\n\n      toolInvocations[toolInvocationIndex] = invocation;\n\n      updateToolInvocationPart(value.toolCallId, invocation);\n\n      execUpdate();\n    },\n    onDataPart(value) {\n      data.push(...value);\n      execUpdate();\n    },\n    onMessageAnnotationsPart(value) {\n      if (messageAnnotations == null) {\n        messageAnnotations = [...value];\n      } else {\n        messageAnnotations.push(...value);\n      }\n\n      execUpdate();\n    },\n    onFinishStepPart(value) {\n      step += 1;\n\n      // reset the current text and reasoning parts\n      currentTextPart = value.isContinued ? currentTextPart : undefined;\n      currentReasoningPart = undefined;\n      currentReasoningTextDetail = undefined;\n    },\n    onStartStepPart(value) {\n      // keep message id stable when we are updating an existing message:\n      if (!replaceLastMessage) {\n        message.id = value.messageId;\n      }\n\n      // add a step boundary part to the message\n      message.parts.push({ type: 'step-start' });\n      execUpdate();\n    },\n    onFinishMessagePart(value) {\n      finishReason = value.finishReason;\n      if (value.usage != null) {\n        usage = calculateLanguageModelUsage(value.usage);\n      }\n    },\n    onErrorPart(error) {\n      throw new Error(error);\n    },\n  });\n\n  onFinish?.({ message, finishReason, usage });\n}\n", "/**\nRepresents the number of tokens used in a prompt and completion.\n */\nexport type LanguageModelUsage = {\n  /**\nThe number of tokens used in the prompt.\n   */\n  promptTokens: number;\n\n  /**\nThe number of tokens used in the completion.\n */\n  completionTokens: number;\n\n  /**\nThe total number of tokens used (promptTokens + completionTokens).\n   */\n  totalTokens: number;\n};\n\n/**\nRepresents the number of tokens used in an embedding.\n */\nexport type EmbeddingModelUsage = {\n  /**\nThe number of tokens used in the embedding.\n   */\n  tokens: number;\n};\n\nexport function calculateLanguageModelUsage({\n  promptTokens,\n  completionTokens,\n}: {\n  promptTokens: number;\n  completionTokens: number;\n}): LanguageModelUsage {\n  return {\n    promptTokens,\n    completionTokens,\n    totalTokens: promptTokens + completionTokens,\n  };\n}\n", "import { JSONValue } from '@ai-sdk/provider';\nimport { safeParseJSON } from '@ai-sdk/provider-utils';\nimport { fixJson } from './fix-json';\n\nexport function parsePartialJson(jsonText: string | undefined): {\n  value: JSONValue | undefined;\n  state:\n    | 'undefined-input'\n    | 'successful-parse'\n    | 'repaired-parse'\n    | 'failed-parse';\n} {\n  if (jsonText === undefined) {\n    return { value: undefined, state: 'undefined-input' };\n  }\n\n  let result = safeParseJSON({ text: jsonText });\n\n  if (result.success) {\n    return { value: result.value, state: 'successful-parse' };\n  }\n\n  result = safeParseJSON({ text: fixJson(jsonText) });\n\n  if (result.success) {\n    return { value: result.value, state: 'repaired-parse' };\n  }\n\n  return { value: undefined, state: 'failed-parse' };\n}\n", "type State =\n  | 'ROOT'\n  | 'FINISH'\n  | 'INSIDE_STRING'\n  | 'INSIDE_STRING_ESCAPE'\n  | 'INSIDE_LITERAL'\n  | 'INSIDE_NUMBER'\n  | 'INSIDE_OBJECT_START'\n  | 'INSIDE_OBJECT_KEY'\n  | 'INSIDE_OBJECT_AFTER_KEY'\n  | 'INSIDE_OBJECT_BEFORE_VALUE'\n  | 'INSIDE_OBJECT_AFTER_VALUE'\n  | 'INSIDE_OBJECT_AFTER_COMMA'\n  | 'INSIDE_ARRAY_START'\n  | 'INSIDE_ARRAY_AFTER_VALUE'\n  | 'INSIDE_ARRAY_AFTER_COMMA';\n\n// Implemented as a scanner with additional fixing\n// that performs a single linear time scan pass over the partial JSON.\n//\n// The states should ideally match relevant states from the JSON spec:\n// https://www.json.org/json-en.html\n//\n// Please note that invalid JSON is not considered/covered, because it\n// is assumed that the resulting JSON will be processed by a standard\n// JSON parser that will detect any invalid JSON.\nexport function fixJson(input: string): string {\n  const stack: State[] = ['ROOT'];\n  let lastValidIndex = -1;\n  let literalStart: number | null = null;\n\n  function processValueStart(char: string, i: number, swapState: State) {\n    {\n      switch (char) {\n        case '\"': {\n          lastValidIndex = i;\n          stack.pop();\n          stack.push(swapState);\n          stack.push('INSIDE_STRING');\n          break;\n        }\n\n        case 'f':\n        case 't':\n        case 'n': {\n          lastValidIndex = i;\n          literalStart = i;\n          stack.pop();\n          stack.push(swapState);\n          stack.push('INSIDE_LITERAL');\n          break;\n        }\n\n        case '-': {\n          stack.pop();\n          stack.push(swapState);\n          stack.push('INSIDE_NUMBER');\n          break;\n        }\n        case '0':\n        case '1':\n        case '2':\n        case '3':\n        case '4':\n        case '5':\n        case '6':\n        case '7':\n        case '8':\n        case '9': {\n          lastValidIndex = i;\n          stack.pop();\n          stack.push(swapState);\n          stack.push('INSIDE_NUMBER');\n          break;\n        }\n\n        case '{': {\n          lastValidIndex = i;\n          stack.pop();\n          stack.push(swapState);\n          stack.push('INSIDE_OBJECT_START');\n          break;\n        }\n\n        case '[': {\n          lastValidIndex = i;\n          stack.pop();\n          stack.push(swapState);\n          stack.push('INSIDE_ARRAY_START');\n          break;\n        }\n      }\n    }\n  }\n\n  function processAfterObjectValue(char: string, i: number) {\n    switch (char) {\n      case ',': {\n        stack.pop();\n        stack.push('INSIDE_OBJECT_AFTER_COMMA');\n        break;\n      }\n      case '}': {\n        lastValidIndex = i;\n        stack.pop();\n        break;\n      }\n    }\n  }\n\n  function processAfterArrayValue(char: string, i: number) {\n    switch (char) {\n      case ',': {\n        stack.pop();\n        stack.push('INSIDE_ARRAY_AFTER_COMMA');\n        break;\n      }\n      case ']': {\n        lastValidIndex = i;\n        stack.pop();\n        break;\n      }\n    }\n  }\n\n  for (let i = 0; i < input.length; i++) {\n    const char = input[i];\n    const currentState = stack[stack.length - 1];\n\n    switch (currentState) {\n      case 'ROOT':\n        processValueStart(char, i, 'FINISH');\n        break;\n\n      case 'INSIDE_OBJECT_START': {\n        switch (char) {\n          case '\"': {\n            stack.pop();\n            stack.push('INSIDE_OBJECT_KEY');\n            break;\n          }\n          case '}': {\n            lastValidIndex = i;\n            stack.pop();\n            break;\n          }\n        }\n        break;\n      }\n\n      case 'INSIDE_OBJECT_AFTER_COMMA': {\n        switch (char) {\n          case '\"': {\n            stack.pop();\n            stack.push('INSIDE_OBJECT_KEY');\n            break;\n          }\n        }\n        break;\n      }\n\n      case 'INSIDE_OBJECT_KEY': {\n        switch (char) {\n          case '\"': {\n            stack.pop();\n            stack.push('INSIDE_OBJECT_AFTER_KEY');\n            break;\n          }\n        }\n        break;\n      }\n\n      case 'INSIDE_OBJECT_AFTER_KEY': {\n        switch (char) {\n          case ':': {\n            stack.pop();\n            stack.push('INSIDE_OBJECT_BEFORE_VALUE');\n\n            break;\n          }\n        }\n        break;\n      }\n\n      case 'INSIDE_OBJECT_BEFORE_VALUE': {\n        processValueStart(char, i, 'INSIDE_OBJECT_AFTER_VALUE');\n        break;\n      }\n\n      case 'INSIDE_OBJECT_AFTER_VALUE': {\n        processAfterObjectValue(char, i);\n        break;\n      }\n\n      case 'INSIDE_STRING': {\n        switch (char) {\n          case '\"': {\n            stack.pop();\n            lastValidIndex = i;\n            break;\n          }\n\n          case '\\\\': {\n            stack.push('INSIDE_STRING_ESCAPE');\n            break;\n          }\n\n          default: {\n            lastValidIndex = i;\n          }\n        }\n\n        break;\n      }\n\n      case 'INSIDE_ARRAY_START': {\n        switch (char) {\n          case ']': {\n            lastValidIndex = i;\n            stack.pop();\n            break;\n          }\n\n          default: {\n            lastValidIndex = i;\n            processValueStart(char, i, 'INSIDE_ARRAY_AFTER_VALUE');\n            break;\n          }\n        }\n        break;\n      }\n\n      case 'INSIDE_ARRAY_AFTER_VALUE': {\n        switch (char) {\n          case ',': {\n            stack.pop();\n            stack.push('INSIDE_ARRAY_AFTER_COMMA');\n            break;\n          }\n\n          case ']': {\n            lastValidIndex = i;\n            stack.pop();\n            break;\n          }\n\n          default: {\n            lastValidIndex = i;\n            break;\n          }\n        }\n\n        break;\n      }\n\n      case 'INSIDE_ARRAY_AFTER_COMMA': {\n        processValueStart(char, i, 'INSIDE_ARRAY_AFTER_VALUE');\n        break;\n      }\n\n      case 'INSIDE_STRING_ESCAPE': {\n        stack.pop();\n        lastValidIndex = i;\n\n        break;\n      }\n\n      case 'INSIDE_NUMBER': {\n        switch (char) {\n          case '0':\n          case '1':\n          case '2':\n          case '3':\n          case '4':\n          case '5':\n          case '6':\n          case '7':\n          case '8':\n          case '9': {\n            lastValidIndex = i;\n            break;\n          }\n\n          case 'e':\n          case 'E':\n          case '-':\n          case '.': {\n            break;\n          }\n\n          case ',': {\n            stack.pop();\n\n            if (stack[stack.length - 1] === 'INSIDE_ARRAY_AFTER_VALUE') {\n              processAfterArrayValue(char, i);\n            }\n\n            if (stack[stack.length - 1] === 'INSIDE_OBJECT_AFTER_VALUE') {\n              processAfterObjectValue(char, i);\n            }\n\n            break;\n          }\n\n          case '}': {\n            stack.pop();\n\n            if (stack[stack.length - 1] === 'INSIDE_OBJECT_AFTER_VALUE') {\n              processAfterObjectValue(char, i);\n            }\n\n            break;\n          }\n\n          case ']': {\n            stack.pop();\n\n            if (stack[stack.length - 1] === 'INSIDE_ARRAY_AFTER_VALUE') {\n              processAfterArrayValue(char, i);\n            }\n\n            break;\n          }\n\n          default: {\n            stack.pop();\n            break;\n          }\n        }\n\n        break;\n      }\n\n      case 'INSIDE_LITERAL': {\n        const partialLiteral = input.substring(literalStart!, i + 1);\n\n        if (\n          !'false'.startsWith(partialLiteral) &&\n          !'true'.startsWith(partialLiteral) &&\n          !'null'.startsWith(partialLiteral)\n        ) {\n          stack.pop();\n\n          if (stack[stack.length - 1] === 'INSIDE_OBJECT_AFTER_VALUE') {\n            processAfterObjectValue(char, i);\n          } else if (stack[stack.length - 1] === 'INSIDE_ARRAY_AFTER_VALUE') {\n            processAfterArrayValue(char, i);\n          }\n        } else {\n          lastValidIndex = i;\n        }\n\n        break;\n      }\n    }\n  }\n\n  let result = input.slice(0, lastValidIndex + 1);\n\n  for (let i = stack.length - 1; i >= 0; i--) {\n    const state = stack[i];\n\n    switch (state) {\n      case 'INSIDE_STRING': {\n        result += '\"';\n        break;\n      }\n\n      case 'INSIDE_OBJECT_KEY':\n      case 'INSIDE_OBJECT_AFTER_KEY':\n      case 'INSIDE_OBJECT_AFTER_COMMA':\n      case 'INSIDE_OBJECT_START':\n      case 'INSIDE_OBJECT_BEFORE_VALUE':\n      case 'INSIDE_OBJECT_AFTER_VALUE': {\n        result += '}';\n        break;\n      }\n\n      case 'INSIDE_ARRAY_START':\n      case 'INSIDE_ARRAY_AFTER_COMMA':\n      case 'INSIDE_ARRAY_AFTER_VALUE': {\n        result += ']';\n        break;\n      }\n\n      case 'INSIDE_LITERAL': {\n        const partialLiteral = input.substring(literalStart!, input.length);\n\n        if ('true'.startsWith(partialLiteral)) {\n          result += 'true'.slice(partialLiteral.length);\n        } else if ('false'.startsWith(partialLiteral)) {\n          result += 'false'.slice(partialLiteral.length);\n        } else if ('null'.startsWith(partialLiteral)) {\n          result += 'null'.slice(partialLiteral.length);\n        }\n      }\n    }\n  }\n\n  return result;\n}\n", "import {\n  LanguageModelV1FinishReason,\n  LanguageModelV1Source,\n} from '@ai-sdk/provider';\nimport { Tool<PERSON>all, ToolResult } from '@ai-sdk/provider-utils';\nimport { JSONValue } from './types';\n\nexport type DataStreamString =\n  `${(typeof DataStreamStringPrefixes)[keyof typeof DataStreamStringPrefixes]}:${string}\\n`;\n\nexport interface DataStreamPart<\n  CODE extends string,\n  NAME extends string,\n  TYPE,\n> {\n  code: CODE;\n  name: NAME;\n  parse: (value: JSONValue) => { type: NAME; value: TYPE };\n}\n\nconst textStreamPart: DataStreamPart<'0', 'text', string> = {\n  code: '0',\n  name: 'text',\n  parse: (value: JSONValue) => {\n    if (typeof value !== 'string') {\n      throw new Error('\"text\" parts expect a string value.');\n    }\n    return { type: 'text', value };\n  },\n};\n\nconst dataStreamPart: DataStreamPart<'2', 'data', Array<JSONValue>> = {\n  code: '2',\n  name: 'data',\n  parse: (value: JSONValue) => {\n    if (!Array.isArray(value)) {\n      throw new Error('\"data\" parts expect an array value.');\n    }\n\n    return { type: 'data', value };\n  },\n};\n\nconst errorStreamPart: DataStreamPart<'3', 'error', string> = {\n  code: '3',\n  name: 'error',\n  parse: (value: JSONValue) => {\n    if (typeof value !== 'string') {\n      throw new Error('\"error\" parts expect a string value.');\n    }\n    return { type: 'error', value };\n  },\n};\n\nconst messageAnnotationsStreamPart: DataStreamPart<\n  '8',\n  'message_annotations',\n  Array<JSONValue>\n> = {\n  code: '8',\n  name: 'message_annotations',\n  parse: (value: JSONValue) => {\n    if (!Array.isArray(value)) {\n      throw new Error('\"message_annotations\" parts expect an array value.');\n    }\n\n    return { type: 'message_annotations', value };\n  },\n};\n\nconst toolCallStreamPart: DataStreamPart<\n  '9',\n  'tool_call',\n  ToolCall<string, any>\n> = {\n  code: '9',\n  name: 'tool_call',\n  parse: (value: JSONValue) => {\n    if (\n      value == null ||\n      typeof value !== 'object' ||\n      !('toolCallId' in value) ||\n      typeof value.toolCallId !== 'string' ||\n      !('toolName' in value) ||\n      typeof value.toolName !== 'string' ||\n      !('args' in value) ||\n      typeof value.args !== 'object'\n    ) {\n      throw new Error(\n        '\"tool_call\" parts expect an object with a \"toolCallId\", \"toolName\", and \"args\" property.',\n      );\n    }\n\n    return {\n      type: 'tool_call',\n      value: value as unknown as ToolCall<string, any>,\n    };\n  },\n};\n\nconst toolResultStreamPart: DataStreamPart<\n  'a',\n  'tool_result',\n  Omit<ToolResult<string, any, any>, 'args' | 'toolName'>\n> = {\n  code: 'a',\n  name: 'tool_result',\n  parse: (value: JSONValue) => {\n    if (\n      value == null ||\n      typeof value !== 'object' ||\n      !('toolCallId' in value) ||\n      typeof value.toolCallId !== 'string' ||\n      !('result' in value)\n    ) {\n      throw new Error(\n        '\"tool_result\" parts expect an object with a \"toolCallId\" and a \"result\" property.',\n      );\n    }\n\n    return {\n      type: 'tool_result',\n      value: value as unknown as Omit<\n        ToolResult<string, any, any>,\n        'args' | 'toolName'\n      >,\n    };\n  },\n};\n\nconst toolCallStreamingStartStreamPart: DataStreamPart<\n  'b',\n  'tool_call_streaming_start',\n  { toolCallId: string; toolName: string }\n> = {\n  code: 'b',\n  name: 'tool_call_streaming_start',\n  parse: (value: JSONValue) => {\n    if (\n      value == null ||\n      typeof value !== 'object' ||\n      !('toolCallId' in value) ||\n      typeof value.toolCallId !== 'string' ||\n      !('toolName' in value) ||\n      typeof value.toolName !== 'string'\n    ) {\n      throw new Error(\n        '\"tool_call_streaming_start\" parts expect an object with a \"toolCallId\" and \"toolName\" property.',\n      );\n    }\n\n    return {\n      type: 'tool_call_streaming_start',\n      value: value as unknown as { toolCallId: string; toolName: string },\n    };\n  },\n};\n\nconst toolCallDeltaStreamPart: DataStreamPart<\n  'c',\n  'tool_call_delta',\n  { toolCallId: string; argsTextDelta: string }\n> = {\n  code: 'c',\n  name: 'tool_call_delta',\n  parse: (value: JSONValue) => {\n    if (\n      value == null ||\n      typeof value !== 'object' ||\n      !('toolCallId' in value) ||\n      typeof value.toolCallId !== 'string' ||\n      !('argsTextDelta' in value) ||\n      typeof value.argsTextDelta !== 'string'\n    ) {\n      throw new Error(\n        '\"tool_call_delta\" parts expect an object with a \"toolCallId\" and \"argsTextDelta\" property.',\n      );\n    }\n\n    return {\n      type: 'tool_call_delta',\n      value: value as unknown as {\n        toolCallId: string;\n        argsTextDelta: string;\n      },\n    };\n  },\n};\n\nconst finishMessageStreamPart: DataStreamPart<\n  'd',\n  'finish_message',\n  {\n    finishReason: LanguageModelV1FinishReason;\n    // TODO v5 remove usage from finish event (only on step-finish)\n    usage?: {\n      promptTokens: number;\n      completionTokens: number;\n    };\n  }\n> = {\n  code: 'd',\n  name: 'finish_message',\n  parse: (value: JSONValue) => {\n    if (\n      value == null ||\n      typeof value !== 'object' ||\n      !('finishReason' in value) ||\n      typeof value.finishReason !== 'string'\n    ) {\n      throw new Error(\n        '\"finish_message\" parts expect an object with a \"finishReason\" property.',\n      );\n    }\n\n    const result: {\n      finishReason: LanguageModelV1FinishReason;\n      usage?: {\n        promptTokens: number;\n        completionTokens: number;\n      };\n    } = {\n      finishReason: value.finishReason as LanguageModelV1FinishReason,\n    };\n\n    if (\n      'usage' in value &&\n      value.usage != null &&\n      typeof value.usage === 'object' &&\n      'promptTokens' in value.usage &&\n      'completionTokens' in value.usage\n    ) {\n      result.usage = {\n        promptTokens:\n          typeof value.usage.promptTokens === 'number'\n            ? value.usage.promptTokens\n            : Number.NaN,\n        completionTokens:\n          typeof value.usage.completionTokens === 'number'\n            ? value.usage.completionTokens\n            : Number.NaN,\n      };\n    }\n\n    return {\n      type: 'finish_message',\n      value: result,\n    };\n  },\n};\n\nconst finishStepStreamPart: DataStreamPart<\n  'e',\n  'finish_step',\n  {\n    isContinued: boolean;\n    finishReason: LanguageModelV1FinishReason;\n    usage?: {\n      promptTokens: number;\n      completionTokens: number;\n    };\n  }\n> = {\n  code: 'e',\n  name: 'finish_step',\n  parse: (value: JSONValue) => {\n    if (\n      value == null ||\n      typeof value !== 'object' ||\n      !('finishReason' in value) ||\n      typeof value.finishReason !== 'string'\n    ) {\n      throw new Error(\n        '\"finish_step\" parts expect an object with a \"finishReason\" property.',\n      );\n    }\n\n    const result: {\n      isContinued: boolean;\n      finishReason: LanguageModelV1FinishReason;\n      usage?: {\n        promptTokens: number;\n        completionTokens: number;\n      };\n    } = {\n      finishReason: value.finishReason as LanguageModelV1FinishReason,\n      isContinued: false,\n    };\n\n    if (\n      'usage' in value &&\n      value.usage != null &&\n      typeof value.usage === 'object' &&\n      'promptTokens' in value.usage &&\n      'completionTokens' in value.usage\n    ) {\n      result.usage = {\n        promptTokens:\n          typeof value.usage.promptTokens === 'number'\n            ? value.usage.promptTokens\n            : Number.NaN,\n        completionTokens:\n          typeof value.usage.completionTokens === 'number'\n            ? value.usage.completionTokens\n            : Number.NaN,\n      };\n    }\n\n    if ('isContinued' in value && typeof value.isContinued === 'boolean') {\n      result.isContinued = value.isContinued;\n    }\n\n    return {\n      type: 'finish_step',\n      value: result,\n    };\n  },\n};\n\nconst startStepStreamPart: DataStreamPart<\n  'f',\n  'start_step',\n  {\n    messageId: string;\n  }\n> = {\n  code: 'f',\n  name: 'start_step',\n  parse: (value: JSONValue) => {\n    if (\n      value == null ||\n      typeof value !== 'object' ||\n      !('messageId' in value) ||\n      typeof value.messageId !== 'string'\n    ) {\n      throw new Error(\n        '\"start_step\" parts expect an object with an \"id\" property.',\n      );\n    }\n\n    return {\n      type: 'start_step',\n      value: {\n        messageId: value.messageId,\n      },\n    };\n  },\n};\n\nconst reasoningStreamPart: DataStreamPart<'g', 'reasoning', string> = {\n  code: 'g',\n  name: 'reasoning',\n  parse: (value: JSONValue) => {\n    if (typeof value !== 'string') {\n      throw new Error('\"reasoning\" parts expect a string value.');\n    }\n    return { type: 'reasoning', value };\n  },\n};\n\nconst sourcePart: DataStreamPart<'h', 'source', LanguageModelV1Source> = {\n  code: 'h',\n  name: 'source',\n  parse: (value: JSONValue) => {\n    if (value == null || typeof value !== 'object') {\n      throw new Error('\"source\" parts expect a Source object.');\n    }\n\n    return {\n      type: 'source',\n      value: value as LanguageModelV1Source,\n    };\n  },\n};\n\nconst redactedReasoningStreamPart: DataStreamPart<\n  'i',\n  'redacted_reasoning',\n  { data: string }\n> = {\n  code: 'i',\n  name: 'redacted_reasoning',\n  parse: (value: JSONValue) => {\n    if (\n      value == null ||\n      typeof value !== 'object' ||\n      !('data' in value) ||\n      typeof value.data !== 'string'\n    ) {\n      throw new Error(\n        '\"redacted_reasoning\" parts expect an object with a \"data\" property.',\n      );\n    }\n    return { type: 'redacted_reasoning', value: { data: value.data } };\n  },\n};\n\nconst reasoningSignatureStreamPart: DataStreamPart<\n  'j',\n  'reasoning_signature',\n  { signature: string }\n> = {\n  code: 'j',\n  name: 'reasoning_signature',\n  parse: (value: JSONValue) => {\n    if (\n      value == null ||\n      typeof value !== 'object' ||\n      !('signature' in value) ||\n      typeof value.signature !== 'string'\n    ) {\n      throw new Error(\n        '\"reasoning_signature\" parts expect an object with a \"signature\" property.',\n      );\n    }\n    return {\n      type: 'reasoning_signature',\n      value: { signature: value.signature },\n    };\n  },\n};\n\nconst fileStreamPart: DataStreamPart<\n  'k',\n  'file',\n  {\n    data: string; // base64 encoded data\n    mimeType: string;\n  }\n> = {\n  code: 'k',\n  name: 'file',\n  parse: (value: JSONValue) => {\n    if (\n      value == null ||\n      typeof value !== 'object' ||\n      !('data' in value) ||\n      typeof value.data !== 'string' ||\n      !('mimeType' in value) ||\n      typeof value.mimeType !== 'string'\n    ) {\n      throw new Error(\n        '\"file\" parts expect an object with a \"data\" and \"mimeType\" property.',\n      );\n    }\n    return { type: 'file', value: value as { data: string; mimeType: string } };\n  },\n};\n\nconst dataStreamParts = [\n  textStreamPart,\n  dataStreamPart,\n  errorStreamPart,\n  messageAnnotationsStreamPart,\n  toolCallStreamPart,\n  toolResultStreamPart,\n  toolCallStreamingStartStreamPart,\n  toolCallDeltaStreamPart,\n  finishMessageStreamPart,\n  finishStepStreamPart,\n  startStepStreamPart,\n  reasoningStreamPart,\n  sourcePart,\n  redactedReasoningStreamPart,\n  reasoningSignatureStreamPart,\n  fileStreamPart,\n] as const;\n\nexport const dataStreamPartsByCode = Object.fromEntries(\n  dataStreamParts.map(part => [part.code, part]),\n) as {\n  [K in (typeof dataStreamParts)[number]['code']]: (typeof dataStreamParts)[number];\n};\n\ntype DataStreamParts = (typeof dataStreamParts)[number];\n\n/**\n * Maps the type of a stream part to its value type.\n */\ntype DataStreamPartValueType = {\n  [P in DataStreamParts as P['name']]: ReturnType<P['parse']>['value'];\n};\n\nexport type DataStreamPartType = ReturnType<DataStreamParts['parse']>;\n\n/**\n * The map of prefixes for data in the stream\n *\n * - 0: Text from the LLM response\n * - 1: (OpenAI) function_call responses\n * - 2: custom JSON added by the user using `Data`\n * - 6: (OpenAI) tool_call responses\n *\n * Example:\n * ```\n * 0:Vercel\n * 0:'s\n * 0: AI\n * 0: AI\n * 0: SDK\n * 0: is great\n * 0:!\n * 2: { \"someJson\": \"value\" }\n * 1: {\"function_call\": {\"name\": \"get_current_weather\", \"arguments\": \"{\\\\n\\\\\"location\\\\\": \\\\\"Charlottesville, Virginia\\\\\",\\\\n\\\\\"format\\\\\": \\\\\"celsius\\\\\"\\\\n}\"}}\n * 6: {\"tool_call\": {\"id\": \"tool_0\", \"type\": \"function\", \"function\": {\"name\": \"get_current_weather\", \"arguments\": \"{\\\\n\\\\\"location\\\\\": \\\\\"Charlottesville, Virginia\\\\\",\\\\n\\\\\"format\\\\\": \\\\\"celsius\\\\\"\\\\n}\"}}}\n *```\n */\nexport const DataStreamStringPrefixes = Object.fromEntries(\n  dataStreamParts.map(part => [part.name, part.code]),\n) as {\n  [K in DataStreamParts['name']]: (typeof dataStreamParts)[number]['code'];\n};\n\nexport const validCodes = dataStreamParts.map(part => part.code);\n\n/**\nParses a stream part from a string.\n\n@param line The string to parse.\n@returns The parsed stream part.\n@throws An error if the string cannot be parsed.\n */\nexport const parseDataStreamPart = (line: string): DataStreamPartType => {\n  const firstSeparatorIndex = line.indexOf(':');\n\n  if (firstSeparatorIndex === -1) {\n    throw new Error('Failed to parse stream string. No separator found.');\n  }\n\n  const prefix = line.slice(0, firstSeparatorIndex);\n\n  if (!validCodes.includes(prefix as keyof typeof dataStreamPartsByCode)) {\n    throw new Error(`Failed to parse stream string. Invalid code ${prefix}.`);\n  }\n\n  const code = prefix as keyof typeof dataStreamPartsByCode;\n\n  const textValue = line.slice(firstSeparatorIndex + 1);\n  const jsonValue: JSONValue = JSON.parse(textValue);\n\n  return dataStreamPartsByCode[code].parse(jsonValue);\n};\n\n/**\nPrepends a string with a prefix from the `StreamChunkPrefixes`, JSON-ifies it,\nand appends a new line.\n\nIt ensures type-safety for the part type and value.\n */\nexport function formatDataStreamPart<T extends keyof DataStreamPartValueType>(\n  type: T,\n  value: DataStreamPartValueType[T],\n): DataStreamString {\n  const streamPart = dataStreamParts.find(part => part.name === type);\n\n  if (!streamPart) {\n    throw new Error(`Invalid stream part type: ${type}`);\n  }\n\n  return `${streamPart.code}:${JSON.stringify(value)}\\n`;\n}\n", "import { DataStreamPartType, parseDataStreamPart } from './data-stream-parts';\n\nconst NEWLINE = '\\n'.charCodeAt(0);\n\n// concatenates all the chunks into a single Uint8Array\nfunction concatChunks(chunks: Uint8Array[], totalLength: number) {\n  const concatenatedChunks = new Uint8Array(totalLength);\n\n  let offset = 0;\n  for (const chunk of chunks) {\n    concatenatedChunks.set(chunk, offset);\n    offset += chunk.length;\n  }\n  chunks.length = 0;\n\n  return concatenatedChunks;\n}\n\nexport async function processDataStream({\n  stream,\n  onTextPart,\n  onReasoningPart,\n  onReasoningSignaturePart,\n  onRedactedReasoningPart,\n  onSourcePart,\n  onFilePart,\n  onDataPart,\n  onErrorPart,\n  onToolCallStreamingStartPart,\n  onToolCallDeltaPart,\n  onToolCallPart,\n  onToolResultPart,\n  onMessageAnnotationsPart,\n  onFinishMessagePart,\n  onFinishStepPart,\n  onStartStepPart,\n}: {\n  stream: ReadableStream<Uint8Array>;\n  onTextPart?: (\n    streamPart: (DataStreamPartType & { type: 'text' })['value'],\n  ) => Promise<void> | void;\n  onReasoningPart?: (\n    streamPart: (DataStreamPartType & { type: 'reasoning' })['value'],\n  ) => Promise<void> | void;\n  onReasoningSignaturePart?: (\n    streamPart: (DataStreamPartType & { type: 'reasoning_signature' })['value'],\n  ) => Promise<void> | void;\n  onRedactedReasoningPart?: (\n    streamPart: (DataStreamPartType & { type: 'redacted_reasoning' })['value'],\n  ) => Promise<void> | void;\n  onFilePart?: (\n    streamPart: (DataStreamPartType & { type: 'file' })['value'],\n  ) => Promise<void> | void;\n  onSourcePart?: (\n    streamPart: (DataStreamPartType & { type: 'source' })['value'],\n  ) => Promise<void> | void;\n  onDataPart?: (\n    streamPart: (DataStreamPartType & { type: 'data' })['value'],\n  ) => Promise<void> | void;\n  onErrorPart?: (\n    streamPart: (DataStreamPartType & { type: 'error' })['value'],\n  ) => Promise<void> | void;\n  onToolCallStreamingStartPart?: (\n    streamPart: (DataStreamPartType & {\n      type: 'tool_call_streaming_start';\n    })['value'],\n  ) => Promise<void> | void;\n  onToolCallDeltaPart?: (\n    streamPart: (DataStreamPartType & { type: 'tool_call_delta' })['value'],\n  ) => Promise<void> | void;\n  onToolCallPart?: (\n    streamPart: (DataStreamPartType & { type: 'tool_call' })['value'],\n  ) => Promise<void> | void;\n  onToolResultPart?: (\n    streamPart: (DataStreamPartType & { type: 'tool_result' })['value'],\n  ) => Promise<void> | void;\n  onMessageAnnotationsPart?: (\n    streamPart: (DataStreamPartType & {\n      type: 'message_annotations';\n    })['value'],\n  ) => Promise<void> | void;\n  onFinishMessagePart?: (\n    streamPart: (DataStreamPartType & { type: 'finish_message' })['value'],\n  ) => Promise<void> | void;\n  onFinishStepPart?: (\n    streamPart: (DataStreamPartType & { type: 'finish_step' })['value'],\n  ) => Promise<void> | void;\n  onStartStepPart?: (\n    streamPart: (DataStreamPartType & { type: 'start_step' })['value'],\n  ) => Promise<void> | void;\n}): Promise<void> {\n  // implementation note: this slightly more complex algorithm is required\n  // to pass the tests in the edge environment.\n\n  const reader = stream.getReader();\n  const decoder = new TextDecoder();\n  const chunks: Uint8Array[] = [];\n  let totalLength = 0;\n\n  while (true) {\n    const { value } = await reader.read();\n\n    if (value) {\n      chunks.push(value);\n      totalLength += value.length;\n      if (value[value.length - 1] !== NEWLINE) {\n        // if the last character is not a newline, we have not read the whole JSON value\n        continue;\n      }\n    }\n\n    if (chunks.length === 0) {\n      break; // we have reached the end of the stream\n    }\n\n    const concatenatedChunks = concatChunks(chunks, totalLength);\n    totalLength = 0;\n\n    const streamParts = decoder\n      .decode(concatenatedChunks, { stream: true })\n      .split('\\n')\n      .filter(line => line !== '') // splitting leaves an empty string at the end\n      .map(parseDataStreamPart);\n\n    for (const { type, value } of streamParts) {\n      switch (type) {\n        case 'text':\n          await onTextPart?.(value);\n          break;\n        case 'reasoning':\n          await onReasoningPart?.(value);\n          break;\n        case 'reasoning_signature':\n          await onReasoningSignaturePart?.(value);\n          break;\n        case 'redacted_reasoning':\n          await onRedactedReasoningPart?.(value);\n          break;\n        case 'file':\n          await onFilePart?.(value);\n          break;\n        case 'source':\n          await onSourcePart?.(value);\n          break;\n        case 'data':\n          await onDataPart?.(value);\n          break;\n        case 'error':\n          await onErrorPart?.(value);\n          break;\n        case 'message_annotations':\n          await onMessageAnnotationsPart?.(value);\n          break;\n        case 'tool_call_streaming_start':\n          await onToolCallStreamingStartPart?.(value);\n          break;\n        case 'tool_call_delta':\n          await onToolCallDeltaPart?.(value);\n          break;\n        case 'tool_call':\n          await onToolCallPart?.(value);\n          break;\n        case 'tool_result':\n          await onToolResultPart?.(value);\n          break;\n        case 'finish_message':\n          await onFinishMessagePart?.(value);\n          break;\n        case 'finish_step':\n          await onFinishStepPart?.(value);\n          break;\n        case 'start_step':\n          await onStartStepPart?.(value);\n          break;\n        default: {\n          const exhaustiveCheck: never = type;\n          throw new Error(`Unknown stream part type: ${exhaustiveCheck}`);\n        }\n      }\n    }\n  }\n}\n", "import { JSONValue } from '@ai-sdk/provider';\nimport { generateId as generateIdFunction } from '@ai-sdk/provider-utils';\nimport { processTextStream } from './process-text-stream';\nimport { TextUIPart, UIMessage, UseChatOptions } from './types';\n\nexport async function processChatTextResponse({\n  stream,\n  update,\n  onFinish,\n  getCurrentDate = () => new Date(),\n  generateId = generateIdFunction,\n}: {\n  stream: ReadableStream<Uint8Array>;\n  update: (options: {\n    message: UIMessage;\n    data: JSONValue[] | undefined;\n    replaceLastMessage: boolean;\n  }) => void;\n  onFinish: UseChatOptions['onFinish'];\n  getCurrentDate?: () => Date;\n  generateId?: () => string;\n}) {\n  const textPart: TextUIPart = { type: 'text', text: '' };\n\n  const resultMessage: UIMessage = {\n    id: generateId(),\n    createdAt: getCurrentDate(),\n    role: 'assistant' as const,\n    content: '',\n    parts: [textPart],\n  };\n\n  await processTextStream({\n    stream,\n    onTextPart: chunk => {\n      resultMessage.content += chunk;\n      textPart.text += chunk;\n\n      // note: creating a new message object is required for Solid.js streaming\n      update({\n        message: { ...resultMessage },\n        data: [],\n        replaceLastMessage: false,\n      });\n    },\n  });\n\n  // in text mode, we don't have usage information or finish reason:\n  onFinish?.(resultMessage, {\n    usage: { completionTokens: NaN, promptTokens: NaN, totalTokens: NaN },\n    finishReason: 'unknown',\n  });\n}\n", "export async function processTextStream({\n  stream,\n  onTextPart,\n}: {\n  stream: ReadableStream<Uint8Array>;\n  onTextPart: (chunk: string) => Promise<void> | void;\n}): Promise<void> {\n  const reader = stream.pipeThrough(new TextDecoderStream()).getReader();\n  while (true) {\n    const { done, value } = await reader.read();\n    if (done) {\n      break;\n    }\n    await onTextPart(value);\n  }\n}\n", "import { processChatResponse } from './process-chat-response';\nimport { processChatTextResponse } from './process-chat-text-response';\nimport { IdGenerator, JSONValue, UIMessage, UseChatOptions } from './types';\n\n// use function to allow for mocking in tests:\nconst getOriginalFetch = () => fetch;\n\nexport async function callChatApi({\n  api,\n  body,\n  streamProtocol = 'data',\n  credentials,\n  headers,\n  abortController,\n  restoreMessagesOnFailure,\n  onResponse,\n  onUpdate,\n  onFinish,\n  onToolCall,\n  generateId,\n  fetch = getOriginalFetch(),\n  lastMessage,\n  requestType = 'generate',\n}: {\n  api: string;\n  body: Record<string, any>;\n  streamProtocol: 'data' | 'text' | undefined;\n  credentials: RequestCredentials | undefined;\n  headers: HeadersInit | undefined;\n  abortController: (() => AbortController | null) | undefined;\n  restoreMessagesOnFailure: () => void;\n  onResponse: ((response: Response) => void | Promise<void>) | undefined;\n  onUpdate: (options: {\n    message: UIMessage;\n    data: JSONValue[] | undefined;\n    replaceLastMessage: boolean;\n  }) => void;\n  onFinish: UseChatOptions['onFinish'];\n  onToolCall: UseChatOptions['onToolCall'];\n  generateId: IdGenerator;\n  fetch: ReturnType<typeof getOriginalFetch> | undefined;\n  lastMessage: UIMessage | undefined;\n  requestType?: 'generate' | 'resume';\n}) {\n  const request =\n    requestType === 'resume'\n      ? fetch(`${api}?chatId=${body.id}`, {\n          method: 'GET',\n          headers: {\n            'Content-Type': 'application/json',\n            ...headers,\n          },\n          signal: abortController?.()?.signal,\n          credentials,\n        })\n      : fetch(api, {\n          method: 'POST',\n          body: JSON.stringify(body),\n          headers: {\n            'Content-Type': 'application/json',\n            ...headers,\n          },\n          signal: abortController?.()?.signal,\n          credentials,\n        });\n\n  const response = await request.catch(err => {\n    restoreMessagesOnFailure();\n    throw err;\n  });\n\n  if (onResponse) {\n    try {\n      await onResponse(response);\n    } catch (err) {\n      throw err;\n    }\n  }\n\n  if (!response.ok) {\n    restoreMessagesOnFailure();\n    throw new Error(\n      (await response.text()) ?? 'Failed to fetch the chat response.',\n    );\n  }\n\n  if (!response.body) {\n    throw new Error('The response body is empty.');\n  }\n\n  switch (streamProtocol) {\n    case 'text': {\n      await processChatTextResponse({\n        stream: response.body,\n        update: onUpdate,\n        onFinish,\n        generateId,\n      });\n      return;\n    }\n\n    case 'data': {\n      await processChatResponse({\n        stream: response.body,\n        update: onUpdate,\n        lastMessage,\n        onToolCall,\n        onFinish({ message, finishReason, usage }) {\n          if (onFinish && message != null) {\n            onFinish(message, { usage, finishReason });\n          }\n        },\n        generateId,\n      });\n      return;\n    }\n\n    default: {\n      const exhaustiveCheck: never = streamProtocol;\n      throw new Error(`Unknown stream protocol: ${exhaustiveCheck}`);\n    }\n  }\n}\n", "import { processTextStream } from './process-text-stream';\nimport { processDataStream } from './process-data-stream';\nimport { JSONValue } from './types';\n\n// use function to allow for mocking in tests:\nconst getOriginalFetch = () => fetch;\n\nexport async function callCompletionApi({\n  api,\n  prompt,\n  credentials,\n  headers,\n  body,\n  streamProtocol = 'data',\n  setCompletion,\n  setLoading,\n  setError,\n  setAbortController,\n  onResponse,\n  onFinish,\n  onError,\n  onData,\n  fetch = getOriginalFetch(),\n}: {\n  api: string;\n  prompt: string;\n  credentials: RequestCredentials | undefined;\n  headers: HeadersInit | undefined;\n  body: Record<string, any>;\n  streamProtocol: 'data' | 'text' | undefined;\n  setCompletion: (completion: string) => void;\n  setLoading: (loading: boolean) => void;\n  setError: (error: Error | undefined) => void;\n  setAbortController: (abortController: AbortController | null) => void;\n  onResponse: ((response: Response) => void | Promise<void>) | undefined;\n  onFinish: ((prompt: string, completion: string) => void) | undefined;\n  onError: ((error: Error) => void) | undefined;\n  onData: ((data: JSONValue[]) => void) | undefined;\n  fetch: ReturnType<typeof getOriginalFetch> | undefined;\n}) {\n  try {\n    setLoading(true);\n    setError(undefined);\n\n    const abortController = new AbortController();\n    setAbortController(abortController);\n\n    // Empty the completion immediately.\n    setCompletion('');\n\n    const response = await fetch(api, {\n      method: 'POST',\n      body: JSON.stringify({\n        prompt,\n        ...body,\n      }),\n      credentials,\n      headers: {\n        'Content-Type': 'application/json',\n        ...headers,\n      },\n      signal: abortController.signal,\n    }).catch(err => {\n      throw err;\n    });\n\n    if (onResponse) {\n      try {\n        await onResponse(response);\n      } catch (err) {\n        throw err;\n      }\n    }\n\n    if (!response.ok) {\n      throw new Error(\n        (await response.text()) ?? 'Failed to fetch the chat response.',\n      );\n    }\n\n    if (!response.body) {\n      throw new Error('The response body is empty.');\n    }\n\n    let result = '';\n\n    switch (streamProtocol) {\n      case 'text': {\n        await processTextStream({\n          stream: response.body,\n          onTextPart: chunk => {\n            result += chunk;\n            setCompletion(result);\n          },\n        });\n        break;\n      }\n      case 'data': {\n        await processDataStream({\n          stream: response.body,\n          onTextPart(value) {\n            result += value;\n            setCompletion(result);\n          },\n          onDataPart(value) {\n            onData?.(value);\n          },\n          onErrorPart(value) {\n            throw new Error(value);\n          },\n        });\n        break;\n      }\n      default: {\n        const exhaustiveCheck: never = streamProtocol;\n        throw new Error(`Unknown stream protocol: ${exhaustiveCheck}`);\n      }\n    }\n\n    if (onFinish) {\n      onFinish(prompt, result);\n    }\n\n    setAbortController(null);\n    return result;\n  } catch (err) {\n    // Ignore abort errors as they are expected.\n    if ((err as any).name === 'AbortError') {\n      setAbortController(null);\n      return null;\n    }\n\n    if (err instanceof Error) {\n      if (onError) {\n        onError(err);\n      }\n    }\n\n    setError(err as Error);\n  } finally {\n    setLoading(false);\n  }\n}\n", "/**\n * Converts a data URL of type text/* to a text string.\n */\nexport function getTextFromDataUrl(dataUrl: string): string {\n  const [header, base64Content] = dataUrl.split(',');\n  const mimeType = header.split(';')[0].split(':')[1];\n\n  if (mimeType == null || base64Content == null) {\n    throw new Error('Invalid data URL format');\n  }\n\n  try {\n    return window.atob(base64Content);\n  } catch (error) {\n    throw new Error(`Error decoding data URL`);\n  }\n}\n", "import { ToolInvocation } from './types';\n\nexport function extractMaxToolInvocationStep(\n  toolInvocations: ToolInvocation[] | undefined,\n): number | undefined {\n  return toolInvocations?.reduce((max, toolInvocation) => {\n    return Math.max(max, toolInvocation.step ?? 0);\n  }, 0);\n}\n", "import {\n  CreateMessage,\n  FileUIPart,\n  Message,\n  ReasoningUIPart,\n  SourceUIPart,\n  StepStartUIPart,\n  TextUIPart,\n  ToolInvocationUIPart,\n  UIMessage,\n} from './types';\n\nexport function getMessageParts(\n  message: Message | CreateMessage | UIMessage,\n): (\n  | TextUIPart\n  | ReasoningUIPart\n  | ToolInvocationUIPart\n  | SourceUIPart\n  | FileUIPart\n  | StepStartUIPart\n)[] {\n  return (\n    message.parts ?? [\n      ...(message.toolInvocations\n        ? message.toolInvocations.map(toolInvocation => ({\n            type: 'tool-invocation' as const,\n            toolInvocation,\n          }))\n        : []),\n      ...(message.reasoning\n        ? [\n            {\n              type: 'reasoning' as const,\n              reasoning: message.reasoning,\n              details: [{ type: 'text' as const, text: message.reasoning }],\n            },\n          ]\n        : []),\n      ...(message.content\n        ? [{ type: 'text' as const, text: message.content }]\n        : []),\n    ]\n  );\n}\n", "import { getMessageParts } from './get-message-parts';\nimport { Message, UIMessage } from './types';\n\nexport function fillMessageParts(messages: Message[]): UIMessage[] {\n  return messages.map(message => ({\n    ...message,\n    parts: getMessageParts(message),\n  }));\n}\n", "/**\n * Performs a deep-equal comparison of two parsed JSON objects.\n *\n * @param {any} obj1 - The first object to compare.\n * @param {any} obj2 - The second object to compare.\n * @returns {boolean} - Returns true if the two objects are deeply equal, false otherwise.\n */\nexport function isDeepEqualData(obj1: any, obj2: any): boolean {\n  // Check for strict equality first\n  if (obj1 === obj2) return true;\n\n  // Check if either is null or undefined\n  if (obj1 == null || obj2 == null) return false;\n\n  // Check if both are objects\n  if (typeof obj1 !== 'object' && typeof obj2 !== 'object')\n    return obj1 === obj2;\n\n  // If they are not strictly equal, they both need to be Objects\n  if (obj1.constructor !== obj2.constructor) return false;\n\n  // Special handling for Date objects\n  if (obj1 instanceof Date && obj2 instanceof Date) {\n    return obj1.getTime() === obj2.getTime();\n  }\n\n  // Handle arrays: compare length and then perform a recursive deep comparison on each item\n  if (Array.isArray(obj1)) {\n    if (obj1.length !== obj2.length) return false;\n    for (let i = 0; i < obj1.length; i++) {\n      if (!isDeepEqualData(obj1[i], obj2[i])) return false;\n    }\n    return true; // All array elements matched\n  }\n\n  // Compare the set of keys in each object\n  const keys1 = Object.keys(obj1);\n  const keys2 = Object.keys(obj2);\n  if (keys1.length !== keys2.length) return false;\n\n  // Check each key-value pair recursively\n  for (const key of keys1) {\n    if (!keys2.includes(key)) return false;\n    if (!isDeepEqualData(obj1[key], obj2[key])) return false;\n  }\n\n  return true; // All keys and values matched\n}\n", "import { Attachment } from './types';\n\nexport async function prepareAttachmentsForRequest(\n  attachmentsFromOptions: FileList | Array<Attachment> | undefined,\n) {\n  if (!attachmentsFromOptions) {\n    return [];\n  }\n\n  // https://github.com/vercel/ai/pull/6045\n  // React-native doesn't have a FileList\n  // global variable, so we need to check for it\n  if (\n    globalThis.FileList &&\n    attachmentsFromOptions instanceof globalThis.FileList\n  ) {\n    return Promise.all(\n      Array.from(attachmentsFromOptions).map(async attachment => {\n        const { name, type } = attachment;\n\n        const dataUrl = await new Promise<string>((resolve, reject) => {\n          const reader = new FileReader();\n          reader.onload = readerEvent => {\n            resolve(readerEvent.target?.result as string);\n          };\n          reader.onerror = error => reject(error);\n          reader.readAsDataURL(attachment);\n        });\n\n        return {\n          name,\n          contentType: type,\n          url: dataUrl,\n        };\n      }),\n    );\n  }\n\n  if (Array.isArray(attachmentsFromOptions)) {\n    return attachmentsFromOptions;\n  }\n\n  throw new Error('Invalid attachments type');\n}\n", "import {\n  AssistantStreamPartType,\n  parseAssistantStreamPart,\n} from './assistant-stream-parts';\n\nconst NEWLINE = '\\n'.charCodeAt(0);\n\n// concatenates all the chunks into a single Uint8Array\nfunction concatChunks(chunks: Uint8Array[], totalLength: number) {\n  const concatenatedChunks = new Uint8Array(totalLength);\n\n  let offset = 0;\n  for (const chunk of chunks) {\n    concatenatedChunks.set(chunk, offset);\n    offset += chunk.length;\n  }\n  chunks.length = 0;\n\n  return concatenatedChunks;\n}\n\nexport async function processAssistantStream({\n  stream,\n  onTextPart,\n  onErrorPart,\n  onAssistantMessagePart,\n  onAssistantControlDataPart,\n  onDataMessagePart,\n}: {\n  stream: ReadableStream<Uint8Array>;\n  onTextPart?: (\n    streamPart: (AssistantStreamPartType & { type: 'text' })['value'],\n  ) => Promise<void> | void;\n  onErrorPart?: (\n    streamPart: (AssistantStreamPartType & { type: 'error' })['value'],\n  ) => Promise<void> | void;\n  onAssistantMessagePart?: (\n    streamPart: (AssistantStreamPartType & {\n      type: 'assistant_message';\n    })['value'],\n  ) => Promise<void> | void;\n  onAssistantControlDataPart?: (\n    streamPart: (AssistantStreamPartType & {\n      type: 'assistant_control_data';\n    })['value'],\n  ) => Promise<void> | void;\n  onDataMessagePart?: (\n    streamPart: (AssistantStreamPartType & { type: 'data_message' })['value'],\n  ) => Promise<void> | void;\n}): Promise<void> {\n  // implementation note: this slightly more complex algorithm is required\n  // to pass the tests in the edge environment.\n\n  const reader = stream.getReader();\n  const decoder = new TextDecoder();\n  const chunks: Uint8Array[] = [];\n  let totalLength = 0;\n\n  while (true) {\n    const { value } = await reader.read();\n\n    if (value) {\n      chunks.push(value);\n      totalLength += value.length;\n      if (value[value.length - 1] !== NEWLINE) {\n        // if the last character is not a newline, we have not read the whole JSON value\n        continue;\n      }\n    }\n\n    if (chunks.length === 0) {\n      break; // we have reached the end of the stream\n    }\n\n    const concatenatedChunks = concatChunks(chunks, totalLength);\n    totalLength = 0;\n\n    const streamParts = decoder\n      .decode(concatenatedChunks, { stream: true })\n      .split('\\n')\n      .filter(line => line !== '')\n      .map(parseAssistantStreamPart);\n\n    for (const { type, value } of streamParts) {\n      switch (type) {\n        case 'text':\n          await onTextPart?.(value);\n          break;\n        case 'error':\n          await onErrorPart?.(value);\n          break;\n        case 'assistant_message':\n          await onAssistantMessagePart?.(value);\n          break;\n        case 'assistant_control_data':\n          await onAssistantControlDataPart?.(value);\n          break;\n        case 'data_message':\n          await onDataMessagePart?.(value);\n          break;\n        default: {\n          const exhaustiveCheck: never = type;\n          throw new Error(`Unknown stream part type: ${exhaustiveCheck}`);\n        }\n      }\n    }\n  }\n}\n", "import { Validator, validatorSymbol } from '@ai-sdk/provider-utils';\nimport { JSONSchema7 } from 'json-schema';\nimport { z } from 'zod';\nimport { zodSchema } from './zod-schema';\n\n/**\n * Used to mark schemas so we can support both Zod and custom schemas.\n */\nconst schemaSymbol = Symbol.for('vercel.ai.schema');\n\nexport type Schema<OBJECT = unknown> = Validator<OBJECT> & {\n  /**\n   * Used to mark schemas so we can support both Zod and custom schemas.\n   */\n  [schemaSymbol]: true;\n\n  /**\n   * Schema type for inference.\n   */\n  _type: OBJECT;\n\n  /**\n   * The JSON Schema for the schema. It is passed to the providers.\n   */\n  readonly jsonSchema: JSONSchema7;\n};\n\n/**\n * Create a schema using a JSON Schema.\n *\n * @param jsonSchema The JSON Schema for the schema.\n * @param options.validate Optional. A validation function for the schema.\n */\nexport function jsonSchema<OBJECT = unknown>(\n  jsonSchema: JSONSchema7,\n  {\n    validate,\n  }: {\n    validate?: (\n      value: unknown,\n    ) => { success: true; value: OBJECT } | { success: false; error: Error };\n  } = {},\n): Schema<OBJECT> {\n  return {\n    [schemaSymbol]: true,\n    _type: undefined as OBJECT, // should never be used directly\n    [validatorSymbol]: true,\n    jsonSchema,\n    validate,\n  };\n}\n\nfunction isSchema(value: unknown): value is Schema {\n  return (\n    typeof value === 'object' &&\n    value !== null &&\n    schemaSymbol in value &&\n    value[schemaSymbol] === true &&\n    'jsonSchema' in value &&\n    'validate' in value\n  );\n}\n\nexport function asSchema<OBJECT>(\n  schema: z.Schema<OBJECT, z.ZodTypeDef, any> | Schema<OBJECT>,\n): Schema<OBJECT> {\n  return isSchema(schema) ? schema : zodSchema(schema);\n}\n", "import { JSONSchema7 } from 'json-schema';\nimport { z } from 'zod';\nimport zodToJsonSchema from 'zod-to-json-schema';\nimport { jsonSchema, Schema } from './schema';\n\nexport function zodSchema<OBJECT>(\n  zodSchema: z.Schema<OBJECT, z.ZodTypeDef, any>,\n  options?: {\n    /**\n     * Enables support for references in the schema.\n     * This is required for recursive schemas, e.g. with `z.lazy`.\n     * However, not all language models and providers support such references.\n     * Defaults to `false`.\n     */\n    useReferences?: boolean;\n  },\n): Schema<OBJECT> {\n  // default to no references (to support openapi conversion for google)\n  const useReferences = options?.useReferences ?? false;\n\n  return jsonSchema(\n    zodToJsonSchema(zodSchema, {\n      $refStrategy: useReferences ? 'root' : 'none',\n      target: 'jsonSchema7', // note: openai mode breaks various gemini conversions\n    }) as JSONSchema7,\n    {\n      validate: value => {\n        const result = zodSchema.safeParse(value);\n        return result.success\n          ? { success: true, value: result.data }\n          : { success: false, error: result.error };\n      },\n    },\n  );\n}\n", "import { extractMaxToolInvocationStep } from './extract-max-tool-invocation-step';\nimport { UIMessage } from './types';\n\nexport function shouldResubmitMessages({\n  originalMaxToolInvocationStep,\n  originalMessageCount,\n  maxSteps,\n  messages,\n}: {\n  originalMaxToolInvocationStep: number | undefined;\n  originalMessageCount: number;\n  maxSteps: number;\n  messages: UIMessage[];\n}) {\n  const lastMessage = messages[messages.length - 1];\n  return (\n    // check if the feature is enabled:\n    maxSteps > 1 &&\n    // ensure there is a last message:\n    lastMessage != null &&\n    // ensure we actually have new steps (to prevent infinite loops in case of errors):\n    (messages.length > originalMessageCount ||\n      extractMaxToolInvocationStep(lastMessage.toolInvocations) !==\n        originalMaxToolInvocationStep) &&\n    // check that next step is possible:\n    isAssistantMessageWithCompletedToolCalls(lastMessage) &&\n    // limit the number of automatic steps:\n    (extractMaxToolInvocationStep(lastMessage.toolInvocations) ?? 0) < maxSteps\n  );\n}\n\n/**\nCheck if the message is an assistant message with completed tool calls.\nThe last step of the message must have at least one tool invocation and\nall tool invocations must have a result.\n */\nexport function isAssistantMessageWithCompletedToolCalls(\n  message: UIMessage,\n): message is UIMessage & {\n  role: 'assistant';\n} {\n  if (message.role !== 'assistant') {\n    return false;\n  }\n\n  const lastStepStartIndex = message.parts.reduce((lastIndex, part, index) => {\n    return part.type === 'step-start' ? index : lastIndex;\n  }, -1);\n\n  const lastStepToolInvocations = message.parts\n    .slice(lastStepStartIndex + 1)\n    .filter(part => part.type === 'tool-invocation');\n\n  return (\n    lastStepToolInvocations.length > 0 &&\n    lastStepToolInvocations.every(part => 'result' in part.toolInvocation)\n  );\n}\n", "import { ToolInvocationUIPart, UIMessage } from './types';\n\n/**\n * Updates the result of a specific tool invocation in the last message of the given messages array.\n *\n * @param {object} params - The parameters object.\n * @param {UIMessage[]} params.messages - An array of messages, from which the last one is updated.\n * @param {string} params.toolCallId - The unique identifier for the tool invocation to update.\n * @param {unknown} params.toolResult - The result object to attach to the tool invocation.\n * @returns {void} This function does not return anything.\n */\nexport function updateToolCallResult({\n  messages,\n  toolCallId,\n  toolResult: result,\n}: {\n  messages: UIMessage[];\n  toolCallId: string;\n  toolResult: unknown;\n}) {\n  const lastMessage = messages[messages.length - 1];\n\n  const invocationPart = lastMessage.parts.find(\n    (part): part is ToolInvocationUIPart =>\n      part.type === 'tool-invocation' &&\n      part.toolInvocation.toolCallId === toolCallId,\n  );\n\n  if (invocationPart == null) {\n    return;\n  }\n\n  const toolResult = {\n    ...invocationPart.toolInvocation,\n    state: 'result' as const,\n    result,\n  };\n\n  invocationPart.toolInvocation = toolResult;\n\n  lastMessage.toolInvocations = lastMessage.toolInvocations?.map(\n    toolInvocation =>\n      toolInvocation.toolCallId === toolCallId ? toolResult : toolInvocation,\n  );\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAEA,IAAAA,yBAA2B;;;ACa3B,IAAM,iBAA2D;AAAA,EAC/D,MAAM;AAAA,EACN,MAAM;AAAA,EACN,OAAO,CAAC,UAAqB;AAC3B,QAAI,OAAO,UAAU,UAAU;AAC7B,YAAM,IAAI,MAAM,qCAAqC;AAAA,IACvD;AACA,WAAO,EAAE,MAAM,QAAQ,MAAM;AAAA,EAC/B;AACF;AAEA,IAAM,kBAA6D;AAAA,EACjE,MAAM;AAAA,EACN,MAAM;AAAA,EACN,OAAO,CAAC,UAAqB;AAC3B,QAAI,OAAO,UAAU,UAAU;AAC7B,YAAM,IAAI,MAAM,sCAAsC;AAAA,IACxD;AACA,WAAO,EAAE,MAAM,SAAS,MAAM;AAAA,EAChC;AACF;AAEA,IAAM,6BAIF;AAAA,EACF,MAAM;AAAA,EACN,MAAM;AAAA,EACN,OAAO,CAAC,UAAqB;AAC3B,QACE,SAAS,QACT,OAAO,UAAU,YACjB,EAAE,QAAQ,UACV,EAAE,UAAU,UACZ,EAAE,aAAa,UACf,OAAO,MAAM,OAAO,YACpB,OAAO,MAAM,SAAS,YACtB,MAAM,SAAS,eACf,CAAC,MAAM,QAAQ,MAAM,OAAO,KAC5B,CAAC,MAAM,QAAQ;AAAA,MACb,UACE,QAAQ,QACR,OAAO,SAAS,YAChB,UAAU,QACV,KAAK,SAAS,UACd,UAAU,QACV,KAAK,QAAQ,QACb,OAAO,KAAK,SAAS,YACrB,WAAW,KAAK,QAChB,OAAO,KAAK,KAAK,UAAU;AAAA,IAC/B,GACA;AACA,YAAM,IAAI;AAAA,QACR;AAAA,MACF;AAAA,IACF;AAEA,WAAO;AAAA,MACL,MAAM;AAAA,MACN;AAAA,IACF;AAAA,EACF;AACF;AAEA,IAAM,iCAOF;AAAA,EACF,MAAM;AAAA,EACN,MAAM;AAAA,EACN,OAAO,CAAC,UAAqB;AAC3B,QACE,SAAS,QACT,OAAO,UAAU,YACjB,EAAE,cAAc,UAChB,EAAE,eAAe,UACjB,OAAO,MAAM,aAAa,YAC1B,OAAO,MAAM,cAAc,UAC3B;AACA,YAAM,IAAI;AAAA,QACR;AAAA,MACF;AAAA,IACF;AAEA,WAAO;AAAA,MACL,MAAM;AAAA,MACN,OAAO;AAAA,QACL,UAAU,MAAM;AAAA,QAChB,WAAW,MAAM;AAAA,MACnB;AAAA,IACF;AAAA,EACF;AACF;AAEA,IAAM,wBAIF;AAAA,EACF,MAAM;AAAA,EACN,MAAM;AAAA,EACN,OAAO,CAAC,UAAqB;AAC3B,QACE,SAAS,QACT,OAAO,UAAU,YACjB,EAAE,UAAU,UACZ,EAAE,UAAU,UACZ,OAAO,MAAM,SAAS,YACtB,MAAM,SAAS,QACf;AACA,YAAM,IAAI;AAAA,QACR;AAAA,MACF;AAAA,IACF;AAEA,WAAO;AAAA,MACL,MAAM;AAAA,MACN;AAAA,IACF;AAAA,EACF;AACF;AAEA,IAAM,uBAAuB;AAAA,EAC3B;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF;AAoBO,IAAM,6BAA6B;AAAA,EACxC,CAAC,eAAe,IAAI,GAAG;AAAA,EACvB,CAAC,gBAAgB,IAAI,GAAG;AAAA,EACxB,CAAC,2BAA2B,IAAI,GAAG;AAAA,EACnC,CAAC,+BAA+B,IAAI,GAAG;AAAA,EACvC,CAAC,sBAAsB,IAAI,GAAG;AAChC;AAEO,IAAM,uBAAuB;AAAA,EAClC,CAAC,eAAe,IAAI,GAAG,eAAe;AAAA,EACtC,CAAC,gBAAgB,IAAI,GAAG,gBAAgB;AAAA,EACxC,CAAC,2BAA2B,IAAI,GAAG,2BAA2B;AAAA,EAC9D,CAAC,+BAA+B,IAAI,GAAG,+BAA+B;AAAA,EACtE,CAAC,sBAAsB,IAAI,GAAG,sBAAsB;AACtD;AAEO,IAAM,aAAa,qBAAqB,IAAI,UAAQ,KAAK,IAAI;AAE7D,IAAM,2BAA2B,CACtC,SAC4B;AAC5B,QAAM,sBAAsB,KAAK,QAAQ,GAAG;AAE5C,MAAI,wBAAwB,IAAI;AAC9B,UAAM,IAAI,MAAM,oDAAoD;AAAA,EACtE;AAEA,QAAM,SAAS,KAAK,MAAM,GAAG,mBAAmB;AAEhD,MAAI,CAAC,WAAW,SAAS,MAAiD,GAAG;AAC3E,UAAM,IAAI,MAAM,+CAA+C,MAAM,GAAG;AAAA,EAC1E;AAEA,QAAM,OAAO;AAEb,QAAM,YAAY,KAAK,MAAM,sBAAsB,CAAC;AACpD,QAAM,YAAuB,KAAK,MAAM,SAAS;AAEjD,SAAO,2BAA2B,IAAI,EAAE,MAAM,SAAS;AACzD;AAEO,SAAS,0BAEd,MAAS,OAA+D;AACxE,QAAM,aAAa,qBAAqB,KAAK,UAAQ,KAAK,SAAS,IAAI;AAEvE,MAAI,CAAC,YAAY;AACf,UAAM,IAAI,MAAM,6BAA6B,IAAI,EAAE;AAAA,EACrD;AAEA,SAAO,GAAG,WAAW,IAAI,IAAI,KAAK,UAAU,KAAK,CAAC;AAAA;AACpD;;;AC1NA,IAAAC,yBAAiD;;;AC6B1C,SAAS,4BAA4B;AAAA,EAC1C;AAAA,EACA;AACF,GAGuB;AACrB,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA,aAAa,eAAe;AAAA,EAC9B;AACF;;;ACzCA,4BAA8B;;;ACyBvB,SAAS,QAAQ,OAAuB;AAC7C,QAAM,QAAiB,CAAC,MAAM;AAC9B,MAAI,iBAAiB;AACrB,MAAI,eAA8B;AAElC,WAAS,kBAAkB,MAAc,GAAW,WAAkB;AACpE;AACE,cAAQ,MAAM;AAAA,QACZ,KAAK,KAAK;AACR,2BAAiB;AACjB,gBAAM,IAAI;AACV,gBAAM,KAAK,SAAS;AACpB,gBAAM,KAAK,eAAe;AAC1B;AAAA,QACF;AAAA,QAEA,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK,KAAK;AACR,2BAAiB;AACjB,yBAAe;AACf,gBAAM,IAAI;AACV,gBAAM,KAAK,SAAS;AACpB,gBAAM,KAAK,gBAAgB;AAC3B;AAAA,QACF;AAAA,QAEA,KAAK,KAAK;AACR,gBAAM,IAAI;AACV,gBAAM,KAAK,SAAS;AACpB,gBAAM,KAAK,eAAe;AAC1B;AAAA,QACF;AAAA,QACA,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK,KAAK;AACR,2BAAiB;AACjB,gBAAM,IAAI;AACV,gBAAM,KAAK,SAAS;AACpB,gBAAM,KAAK,eAAe;AAC1B;AAAA,QACF;AAAA,QAEA,KAAK,KAAK;AACR,2BAAiB;AACjB,gBAAM,IAAI;AACV,gBAAM,KAAK,SAAS;AACpB,gBAAM,KAAK,qBAAqB;AAChC;AAAA,QACF;AAAA,QAEA,KAAK,KAAK;AACR,2BAAiB;AACjB,gBAAM,IAAI;AACV,gBAAM,KAAK,SAAS;AACpB,gBAAM,KAAK,oBAAoB;AAC/B;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAEA,WAAS,wBAAwB,MAAc,GAAW;AACxD,YAAQ,MAAM;AAAA,MACZ,KAAK,KAAK;AACR,cAAM,IAAI;AACV,cAAM,KAAK,2BAA2B;AACtC;AAAA,MACF;AAAA,MACA,KAAK,KAAK;AACR,yBAAiB;AACjB,cAAM,IAAI;AACV;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAEA,WAAS,uBAAuB,MAAc,GAAW;AACvD,YAAQ,MAAM;AAAA,MACZ,KAAK,KAAK;AACR,cAAM,IAAI;AACV,cAAM,KAAK,0BAA0B;AACrC;AAAA,MACF;AAAA,MACA,KAAK,KAAK;AACR,yBAAiB;AACjB,cAAM,IAAI;AACV;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAEA,WAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACrC,UAAM,OAAO,MAAM,CAAC;AACpB,UAAM,eAAe,MAAM,MAAM,SAAS,CAAC;AAE3C,YAAQ,cAAc;AAAA,MACpB,KAAK;AACH,0BAAkB,MAAM,GAAG,QAAQ;AACnC;AAAA,MAEF,KAAK,uBAAuB;AAC1B,gBAAQ,MAAM;AAAA,UACZ,KAAK,KAAK;AACR,kBAAM,IAAI;AACV,kBAAM,KAAK,mBAAmB;AAC9B;AAAA,UACF;AAAA,UACA,KAAK,KAAK;AACR,6BAAiB;AACjB,kBAAM,IAAI;AACV;AAAA,UACF;AAAA,QACF;AACA;AAAA,MACF;AAAA,MAEA,KAAK,6BAA6B;AAChC,gBAAQ,MAAM;AAAA,UACZ,KAAK,KAAK;AACR,kBAAM,IAAI;AACV,kBAAM,KAAK,mBAAmB;AAC9B;AAAA,UACF;AAAA,QACF;AACA;AAAA,MACF;AAAA,MAEA,KAAK,qBAAqB;AACxB,gBAAQ,MAAM;AAAA,UACZ,KAAK,KAAK;AACR,kBAAM,IAAI;AACV,kBAAM,KAAK,yBAAyB;AACpC;AAAA,UACF;AAAA,QACF;AACA;AAAA,MACF;AAAA,MAEA,KAAK,2BAA2B;AAC9B,gBAAQ,MAAM;AAAA,UACZ,KAAK,KAAK;AACR,kBAAM,IAAI;AACV,kBAAM,KAAK,4BAA4B;AAEvC;AAAA,UACF;AAAA,QACF;AACA;AAAA,MACF;AAAA,MAEA,KAAK,8BAA8B;AACjC,0BAAkB,MAAM,GAAG,2BAA2B;AACtD;AAAA,MACF;AAAA,MAEA,KAAK,6BAA6B;AAChC,gCAAwB,MAAM,CAAC;AAC/B;AAAA,MACF;AAAA,MAEA,KAAK,iBAAiB;AACpB,gBAAQ,MAAM;AAAA,UACZ,KAAK,KAAK;AACR,kBAAM,IAAI;AACV,6BAAiB;AACjB;AAAA,UACF;AAAA,UAEA,KAAK,MAAM;AACT,kBAAM,KAAK,sBAAsB;AACjC;AAAA,UACF;AAAA,UAEA,SAAS;AACP,6BAAiB;AAAA,UACnB;AAAA,QACF;AAEA;AAAA,MACF;AAAA,MAEA,KAAK,sBAAsB;AACzB,gBAAQ,MAAM;AAAA,UACZ,KAAK,KAAK;AACR,6BAAiB;AACjB,kBAAM,IAAI;AACV;AAAA,UACF;AAAA,UAEA,SAAS;AACP,6BAAiB;AACjB,8BAAkB,MAAM,GAAG,0BAA0B;AACrD;AAAA,UACF;AAAA,QACF;AACA;AAAA,MACF;AAAA,MAEA,KAAK,4BAA4B;AAC/B,gBAAQ,MAAM;AAAA,UACZ,KAAK,KAAK;AACR,kBAAM,IAAI;AACV,kBAAM,KAAK,0BAA0B;AACrC;AAAA,UACF;AAAA,UAEA,KAAK,KAAK;AACR,6BAAiB;AACjB,kBAAM,IAAI;AACV;AAAA,UACF;AAAA,UAEA,SAAS;AACP,6BAAiB;AACjB;AAAA,UACF;AAAA,QACF;AAEA;AAAA,MACF;AAAA,MAEA,KAAK,4BAA4B;AAC/B,0BAAkB,MAAM,GAAG,0BAA0B;AACrD;AAAA,MACF;AAAA,MAEA,KAAK,wBAAwB;AAC3B,cAAM,IAAI;AACV,yBAAiB;AAEjB;AAAA,MACF;AAAA,MAEA,KAAK,iBAAiB;AACpB,gBAAQ,MAAM;AAAA,UACZ,KAAK;AAAA,UACL,KAAK;AAAA,UACL,KAAK;AAAA,UACL,KAAK;AAAA,UACL,KAAK;AAAA,UACL,KAAK;AAAA,UACL,KAAK;AAAA,UACL,KAAK;AAAA,UACL,KAAK;AAAA,UACL,KAAK,KAAK;AACR,6BAAiB;AACjB;AAAA,UACF;AAAA,UAEA,KAAK;AAAA,UACL,KAAK;AAAA,UACL,KAAK;AAAA,UACL,KAAK,KAAK;AACR;AAAA,UACF;AAAA,UAEA,KAAK,KAAK;AACR,kBAAM,IAAI;AAEV,gBAAI,MAAM,MAAM,SAAS,CAAC,MAAM,4BAA4B;AAC1D,qCAAuB,MAAM,CAAC;AAAA,YAChC;AAEA,gBAAI,MAAM,MAAM,SAAS,CAAC,MAAM,6BAA6B;AAC3D,sCAAwB,MAAM,CAAC;AAAA,YACjC;AAEA;AAAA,UACF;AAAA,UAEA,KAAK,KAAK;AACR,kBAAM,IAAI;AAEV,gBAAI,MAAM,MAAM,SAAS,CAAC,MAAM,6BAA6B;AAC3D,sCAAwB,MAAM,CAAC;AAAA,YACjC;AAEA;AAAA,UACF;AAAA,UAEA,KAAK,KAAK;AACR,kBAAM,IAAI;AAEV,gBAAI,MAAM,MAAM,SAAS,CAAC,MAAM,4BAA4B;AAC1D,qCAAuB,MAAM,CAAC;AAAA,YAChC;AAEA;AAAA,UACF;AAAA,UAEA,SAAS;AACP,kBAAM,IAAI;AACV;AAAA,UACF;AAAA,QACF;AAEA;AAAA,MACF;AAAA,MAEA,KAAK,kBAAkB;AACrB,cAAM,iBAAiB,MAAM,UAAU,cAAe,IAAI,CAAC;AAE3D,YACE,CAAC,QAAQ,WAAW,cAAc,KAClC,CAAC,OAAO,WAAW,cAAc,KACjC,CAAC,OAAO,WAAW,cAAc,GACjC;AACA,gBAAM,IAAI;AAEV,cAAI,MAAM,MAAM,SAAS,CAAC,MAAM,6BAA6B;AAC3D,oCAAwB,MAAM,CAAC;AAAA,UACjC,WAAW,MAAM,MAAM,SAAS,CAAC,MAAM,4BAA4B;AACjE,mCAAuB,MAAM,CAAC;AAAA,UAChC;AAAA,QACF,OAAO;AACL,2BAAiB;AAAA,QACnB;AAEA;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAEA,MAAI,SAAS,MAAM,MAAM,GAAG,iBAAiB,CAAC;AAE9C,WAAS,IAAI,MAAM,SAAS,GAAG,KAAK,GAAG,KAAK;AAC1C,UAAM,QAAQ,MAAM,CAAC;AAErB,YAAQ,OAAO;AAAA,MACb,KAAK,iBAAiB;AACpB,kBAAU;AACV;AAAA,MACF;AAAA,MAEA,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK,6BAA6B;AAChC,kBAAU;AACV;AAAA,MACF;AAAA,MAEA,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK,4BAA4B;AAC/B,kBAAU;AACV;AAAA,MACF;AAAA,MAEA,KAAK,kBAAkB;AACrB,cAAM,iBAAiB,MAAM,UAAU,cAAe,MAAM,MAAM;AAElE,YAAI,OAAO,WAAW,cAAc,GAAG;AACrC,oBAAU,OAAO,MAAM,eAAe,MAAM;AAAA,QAC9C,WAAW,QAAQ,WAAW,cAAc,GAAG;AAC7C,oBAAU,QAAQ,MAAM,eAAe,MAAM;AAAA,QAC/C,WAAW,OAAO,WAAW,cAAc,GAAG;AAC5C,oBAAU,OAAO,MAAM,eAAe,MAAM;AAAA,QAC9C;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAEA,SAAO;AACT;;;AD5YO,SAAS,iBAAiB,UAO/B;AACA,MAAI,aAAa,QAAW;AAC1B,WAAO,EAAE,OAAO,QAAW,OAAO,kBAAkB;AAAA,EACtD;AAEA,MAAI,aAAS,qCAAc,EAAE,MAAM,SAAS,CAAC;AAE7C,MAAI,OAAO,SAAS;AAClB,WAAO,EAAE,OAAO,OAAO,OAAO,OAAO,mBAAmB;AAAA,EAC1D;AAEA,eAAS,qCAAc,EAAE,MAAM,QAAQ,QAAQ,EAAE,CAAC;AAElD,MAAI,OAAO,SAAS;AAClB,WAAO,EAAE,OAAO,OAAO,OAAO,OAAO,iBAAiB;AAAA,EACxD;AAEA,SAAO,EAAE,OAAO,QAAW,OAAO,eAAe;AACnD;;;AETA,IAAMC,kBAAsD;AAAA,EAC1D,MAAM;AAAA,EACN,MAAM;AAAA,EACN,OAAO,CAAC,UAAqB;AAC3B,QAAI,OAAO,UAAU,UAAU;AAC7B,YAAM,IAAI,MAAM,qCAAqC;AAAA,IACvD;AACA,WAAO,EAAE,MAAM,QAAQ,MAAM;AAAA,EAC/B;AACF;AAEA,IAAM,iBAAgE;AAAA,EACpE,MAAM;AAAA,EACN,MAAM;AAAA,EACN,OAAO,CAAC,UAAqB;AAC3B,QAAI,CAAC,MAAM,QAAQ,KAAK,GAAG;AACzB,YAAM,IAAI,MAAM,qCAAqC;AAAA,IACvD;AAEA,WAAO,EAAE,MAAM,QAAQ,MAAM;AAAA,EAC/B;AACF;AAEA,IAAMC,mBAAwD;AAAA,EAC5D,MAAM;AAAA,EACN,MAAM;AAAA,EACN,OAAO,CAAC,UAAqB;AAC3B,QAAI,OAAO,UAAU,UAAU;AAC7B,YAAM,IAAI,MAAM,sCAAsC;AAAA,IACxD;AACA,WAAO,EAAE,MAAM,SAAS,MAAM;AAAA,EAChC;AACF;AAEA,IAAM,+BAIF;AAAA,EACF,MAAM;AAAA,EACN,MAAM;AAAA,EACN,OAAO,CAAC,UAAqB;AAC3B,QAAI,CAAC,MAAM,QAAQ,KAAK,GAAG;AACzB,YAAM,IAAI,MAAM,oDAAoD;AAAA,IACtE;AAEA,WAAO,EAAE,MAAM,uBAAuB,MAAM;AAAA,EAC9C;AACF;AAEA,IAAM,qBAIF;AAAA,EACF,MAAM;AAAA,EACN,MAAM;AAAA,EACN,OAAO,CAAC,UAAqB;AAC3B,QACE,SAAS,QACT,OAAO,UAAU,YACjB,EAAE,gBAAgB,UAClB,OAAO,MAAM,eAAe,YAC5B,EAAE,cAAc,UAChB,OAAO,MAAM,aAAa,YAC1B,EAAE,UAAU,UACZ,OAAO,MAAM,SAAS,UACtB;AACA,YAAM,IAAI;AAAA,QACR;AAAA,MACF;AAAA,IACF;AAEA,WAAO;AAAA,MACL,MAAM;AAAA,MACN;AAAA,IACF;AAAA,EACF;AACF;AAEA,IAAM,uBAIF;AAAA,EACF,MAAM;AAAA,EACN,MAAM;AAAA,EACN,OAAO,CAAC,UAAqB;AAC3B,QACE,SAAS,QACT,OAAO,UAAU,YACjB,EAAE,gBAAgB,UAClB,OAAO,MAAM,eAAe,YAC5B,EAAE,YAAY,QACd;AACA,YAAM,IAAI;AAAA,QACR;AAAA,MACF;AAAA,IACF;AAEA,WAAO;AAAA,MACL,MAAM;AAAA,MACN;AAAA,IAIF;AAAA,EACF;AACF;AAEA,IAAM,mCAIF;AAAA,EACF,MAAM;AAAA,EACN,MAAM;AAAA,EACN,OAAO,CAAC,UAAqB;AAC3B,QACE,SAAS,QACT,OAAO,UAAU,YACjB,EAAE,gBAAgB,UAClB,OAAO,MAAM,eAAe,YAC5B,EAAE,cAAc,UAChB,OAAO,MAAM,aAAa,UAC1B;AACA,YAAM,IAAI;AAAA,QACR;AAAA,MACF;AAAA,IACF;AAEA,WAAO;AAAA,MACL,MAAM;AAAA,MACN;AAAA,IACF;AAAA,EACF;AACF;AAEA,IAAM,0BAIF;AAAA,EACF,MAAM;AAAA,EACN,MAAM;AAAA,EACN,OAAO,CAAC,UAAqB;AAC3B,QACE,SAAS,QACT,OAAO,UAAU,YACjB,EAAE,gBAAgB,UAClB,OAAO,MAAM,eAAe,YAC5B,EAAE,mBAAmB,UACrB,OAAO,MAAM,kBAAkB,UAC/B;AACA,YAAM,IAAI;AAAA,QACR;AAAA,MACF;AAAA,IACF;AAEA,WAAO;AAAA,MACL,MAAM;AAAA,MACN;AAAA,IAIF;AAAA,EACF;AACF;AAEA,IAAM,0BAWF;AAAA,EACF,MAAM;AAAA,EACN,MAAM;AAAA,EACN,OAAO,CAAC,UAAqB;AAC3B,QACE,SAAS,QACT,OAAO,UAAU,YACjB,EAAE,kBAAkB,UACpB,OAAO,MAAM,iBAAiB,UAC9B;AACA,YAAM,IAAI;AAAA,QACR;AAAA,MACF;AAAA,IACF;AAEA,UAAM,SAMF;AAAA,MACF,cAAc,MAAM;AAAA,IACtB;AAEA,QACE,WAAW,SACX,MAAM,SAAS,QACf,OAAO,MAAM,UAAU,YACvB,kBAAkB,MAAM,SACxB,sBAAsB,MAAM,OAC5B;AACA,aAAO,QAAQ;AAAA,QACb,cACE,OAAO,MAAM,MAAM,iBAAiB,WAChC,MAAM,MAAM,eACZ,OAAO;AAAA,QACb,kBACE,OAAO,MAAM,MAAM,qBAAqB,WACpC,MAAM,MAAM,mBACZ,OAAO;AAAA,MACf;AAAA,IACF;AAEA,WAAO;AAAA,MACL,MAAM;AAAA,MACN,OAAO;AAAA,IACT;AAAA,EACF;AACF;AAEA,IAAM,uBAWF;AAAA,EACF,MAAM;AAAA,EACN,MAAM;AAAA,EACN,OAAO,CAAC,UAAqB;AAC3B,QACE,SAAS,QACT,OAAO,UAAU,YACjB,EAAE,kBAAkB,UACpB,OAAO,MAAM,iBAAiB,UAC9B;AACA,YAAM,IAAI;AAAA,QACR;AAAA,MACF;AAAA,IACF;AAEA,UAAM,SAOF;AAAA,MACF,cAAc,MAAM;AAAA,MACpB,aAAa;AAAA,IACf;AAEA,QACE,WAAW,SACX,MAAM,SAAS,QACf,OAAO,MAAM,UAAU,YACvB,kBAAkB,MAAM,SACxB,sBAAsB,MAAM,OAC5B;AACA,aAAO,QAAQ;AAAA,QACb,cACE,OAAO,MAAM,MAAM,iBAAiB,WAChC,MAAM,MAAM,eACZ,OAAO;AAAA,QACb,kBACE,OAAO,MAAM,MAAM,qBAAqB,WACpC,MAAM,MAAM,mBACZ,OAAO;AAAA,MACf;AAAA,IACF;AAEA,QAAI,iBAAiB,SAAS,OAAO,MAAM,gBAAgB,WAAW;AACpE,aAAO,cAAc,MAAM;AAAA,IAC7B;AAEA,WAAO;AAAA,MACL,MAAM;AAAA,MACN,OAAO;AAAA,IACT;AAAA,EACF;AACF;AAEA,IAAM,sBAMF;AAAA,EACF,MAAM;AAAA,EACN,MAAM;AAAA,EACN,OAAO,CAAC,UAAqB;AAC3B,QACE,SAAS,QACT,OAAO,UAAU,YACjB,EAAE,eAAe,UACjB,OAAO,MAAM,cAAc,UAC3B;AACA,YAAM,IAAI;AAAA,QACR;AAAA,MACF;AAAA,IACF;AAEA,WAAO;AAAA,MACL,MAAM;AAAA,MACN,OAAO;AAAA,QACL,WAAW,MAAM;AAAA,MACnB;AAAA,IACF;AAAA,EACF;AACF;AAEA,IAAM,sBAAgE;AAAA,EACpE,MAAM;AAAA,EACN,MAAM;AAAA,EACN,OAAO,CAAC,UAAqB;AAC3B,QAAI,OAAO,UAAU,UAAU;AAC7B,YAAM,IAAI,MAAM,0CAA0C;AAAA,IAC5D;AACA,WAAO,EAAE,MAAM,aAAa,MAAM;AAAA,EACpC;AACF;AAEA,IAAM,aAAmE;AAAA,EACvE,MAAM;AAAA,EACN,MAAM;AAAA,EACN,OAAO,CAAC,UAAqB;AAC3B,QAAI,SAAS,QAAQ,OAAO,UAAU,UAAU;AAC9C,YAAM,IAAI,MAAM,wCAAwC;AAAA,IAC1D;AAEA,WAAO;AAAA,MACL,MAAM;AAAA,MACN;AAAA,IACF;AAAA,EACF;AACF;AAEA,IAAM,8BAIF;AAAA,EACF,MAAM;AAAA,EACN,MAAM;AAAA,EACN,OAAO,CAAC,UAAqB;AAC3B,QACE,SAAS,QACT,OAAO,UAAU,YACjB,EAAE,UAAU,UACZ,OAAO,MAAM,SAAS,UACtB;AACA,YAAM,IAAI;AAAA,QACR;AAAA,MACF;AAAA,IACF;AACA,WAAO,EAAE,MAAM,sBAAsB,OAAO,EAAE,MAAM,MAAM,KAAK,EAAE;AAAA,EACnE;AACF;AAEA,IAAM,+BAIF;AAAA,EACF,MAAM;AAAA,EACN,MAAM;AAAA,EACN,OAAO,CAAC,UAAqB;AAC3B,QACE,SAAS,QACT,OAAO,UAAU,YACjB,EAAE,eAAe,UACjB,OAAO,MAAM,cAAc,UAC3B;AACA,YAAM,IAAI;AAAA,QACR;AAAA,MACF;AAAA,IACF;AACA,WAAO;AAAA,MACL,MAAM;AAAA,MACN,OAAO,EAAE,WAAW,MAAM,UAAU;AAAA,IACtC;AAAA,EACF;AACF;AAEA,IAAM,iBAOF;AAAA,EACF,MAAM;AAAA,EACN,MAAM;AAAA,EACN,OAAO,CAAC,UAAqB;AAC3B,QACE,SAAS,QACT,OAAO,UAAU,YACjB,EAAE,UAAU,UACZ,OAAO,MAAM,SAAS,YACtB,EAAE,cAAc,UAChB,OAAO,MAAM,aAAa,UAC1B;AACA,YAAM,IAAI;AAAA,QACR;AAAA,MACF;AAAA,IACF;AACA,WAAO,EAAE,MAAM,QAAQ,MAAmD;AAAA,EAC5E;AACF;AAEA,IAAM,kBAAkB;AAAA,EACtBD;AAAA,EACA;AAAA,EACAC;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF;AAEO,IAAM,wBAAwB,OAAO;AAAA,EAC1C,gBAAgB,IAAI,UAAQ,CAAC,KAAK,MAAM,IAAI,CAAC;AAC/C;AAqCO,IAAM,2BAA2B,OAAO;AAAA,EAC7C,gBAAgB,IAAI,UAAQ,CAAC,KAAK,MAAM,KAAK,IAAI,CAAC;AACpD;AAIO,IAAMC,cAAa,gBAAgB,IAAI,UAAQ,KAAK,IAAI;AASxD,IAAM,sBAAsB,CAAC,SAAqC;AACvE,QAAM,sBAAsB,KAAK,QAAQ,GAAG;AAE5C,MAAI,wBAAwB,IAAI;AAC9B,UAAM,IAAI,MAAM,oDAAoD;AAAA,EACtE;AAEA,QAAM,SAAS,KAAK,MAAM,GAAG,mBAAmB;AAEhD,MAAI,CAACA,YAAW,SAAS,MAA4C,GAAG;AACtE,UAAM,IAAI,MAAM,+CAA+C,MAAM,GAAG;AAAA,EAC1E;AAEA,QAAM,OAAO;AAEb,QAAM,YAAY,KAAK,MAAM,sBAAsB,CAAC;AACpD,QAAM,YAAuB,KAAK,MAAM,SAAS;AAEjD,SAAO,sBAAsB,IAAI,EAAE,MAAM,SAAS;AACpD;AAQO,SAAS,qBACd,MACA,OACkB;AAClB,QAAM,aAAa,gBAAgB,KAAK,UAAQ,KAAK,SAAS,IAAI;AAElE,MAAI,CAAC,YAAY;AACf,UAAM,IAAI,MAAM,6BAA6B,IAAI,EAAE;AAAA,EACrD;AAEA,SAAO,GAAG,WAAW,IAAI,IAAI,KAAK,UAAU,KAAK,CAAC;AAAA;AACpD;;;AC9iBA,IAAM,UAAU,KAAK,WAAW,CAAC;AAGjC,SAAS,aAAa,QAAsB,aAAqB;AAC/D,QAAM,qBAAqB,IAAI,WAAW,WAAW;AAErD,MAAI,SAAS;AACb,aAAW,SAAS,QAAQ;AAC1B,uBAAmB,IAAI,OAAO,MAAM;AACpC,cAAU,MAAM;AAAA,EAClB;AACA,SAAO,SAAS;AAEhB,SAAO;AACT;AAEA,eAAsB,kBAAkB;AAAA,EACtC;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF,GAsDkB;AAIhB,QAAM,SAAS,OAAO,UAAU;AAChC,QAAM,UAAU,IAAI,YAAY;AAChC,QAAM,SAAuB,CAAC;AAC9B,MAAI,cAAc;AAElB,SAAO,MAAM;AACX,UAAM,EAAE,MAAM,IAAI,MAAM,OAAO,KAAK;AAEpC,QAAI,OAAO;AACT,aAAO,KAAK,KAAK;AACjB,qBAAe,MAAM;AACrB,UAAI,MAAM,MAAM,SAAS,CAAC,MAAM,SAAS;AAEvC;AAAA,MACF;AAAA,IACF;AAEA,QAAI,OAAO,WAAW,GAAG;AACvB;AAAA,IACF;AAEA,UAAM,qBAAqB,aAAa,QAAQ,WAAW;AAC3D,kBAAc;AAEd,UAAM,cAAc,QACjB,OAAO,oBAAoB,EAAE,QAAQ,KAAK,CAAC,EAC3C,MAAM,IAAI,EACV,OAAO,UAAQ,SAAS,EAAE,EAC1B,IAAI,mBAAmB;AAE1B,eAAW,EAAE,MAAM,OAAAC,OAAM,KAAK,aAAa;AACzC,cAAQ,MAAM;AAAA,QACZ,KAAK;AACH,iBAAM,yCAAaA;AACnB;AAAA,QACF,KAAK;AACH,iBAAM,mDAAkBA;AACxB;AAAA,QACF,KAAK;AACH,iBAAM,qEAA2BA;AACjC;AAAA,QACF,KAAK;AACH,iBAAM,mEAA0BA;AAChC;AAAA,QACF,KAAK;AACH,iBAAM,yCAAaA;AACnB;AAAA,QACF,KAAK;AACH,iBAAM,6CAAeA;AACrB;AAAA,QACF,KAAK;AACH,iBAAM,yCAAaA;AACnB;AAAA,QACF,KAAK;AACH,iBAAM,2CAAcA;AACpB;AAAA,QACF,KAAK;AACH,iBAAM,qEAA2BA;AACjC;AAAA,QACF,KAAK;AACH,iBAAM,6EAA+BA;AACrC;AAAA,QACF,KAAK;AACH,iBAAM,2DAAsBA;AAC5B;AAAA,QACF,KAAK;AACH,iBAAM,iDAAiBA;AACvB;AAAA,QACF,KAAK;AACH,iBAAM,qDAAmBA;AACzB;AAAA,QACF,KAAK;AACH,iBAAM,2DAAsBA;AAC5B;AAAA,QACF,KAAK;AACH,iBAAM,qDAAmBA;AACzB;AAAA,QACF,KAAK;AACH,iBAAM,mDAAkBA;AACxB;AAAA,QACF,SAAS;AACP,gBAAM,kBAAyB;AAC/B,gBAAM,IAAI,MAAM,6BAA6B,eAAe,EAAE;AAAA,QAChE;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACF;;;ALnKA,eAAsB,oBAAoB;AAAA,EACxC;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA,YAAAC,cAAa,uBAAAC;AAAA,EACb,iBAAiB,MAAM,oBAAI,KAAK;AAAA,EAChC;AACF,GAgBG;AA1CH;AA2CE,QAAM,sBAAqB,2CAAa,UAAS;AACjD,MAAI,OAAO,qBACP;AAAA,IAEC,uBAAY,oBAAZ,mBAA6B,OAAO,CAAC,KAAK,mBAAmB;AA/CpE,QAAAC;AAgDQ,WAAO,KAAK,IAAI,MAAKA,MAAA,eAAe,SAAf,OAAAA,MAAuB,CAAC;AAAA,EAC/C,GAAG,OAFF,YAEQ,KACT;AAEJ,QAAM,UAAqB,qBACvB,gBAAgB,WAAW,IAC3B;AAAA,IACE,IAAIF,YAAW;AAAA,IACf,WAAW,eAAe;AAAA,IAC1B,MAAM;AAAA,IACN,SAAS;AAAA,IACT,OAAO,CAAC;AAAA,EACV;AAEJ,MAAI,kBAA0C;AAC9C,MAAI,uBAAoD;AACxD,MAAI,6BAEY;AAEhB,WAAS,yBACP,YACA,YACA;AACA,UAAM,OAAO,QAAQ,MAAM;AAAA,MACzB,CAAAG,UACEA,MAAK,SAAS,qBACdA,MAAK,eAAe,eAAe;AAAA,IACvC;AAEA,QAAI,QAAQ,MAAM;AAChB,WAAK,iBAAiB;AAAA,IACxB,OAAO;AACL,cAAQ,MAAM,KAAK;AAAA,QACjB,MAAM;AAAA,QACN,gBAAgB;AAAA,MAClB,CAAC;AAAA,IACH;AAAA,EACF;AAEA,QAAM,OAAoB,CAAC;AAG3B,MAAI,qBAA8C,qBAC9C,2CAAa,cACb;AAGJ,QAAM,mBAGF,CAAC;AAEL,MAAI,QAA4B;AAAA,IAC9B,kBAAkB;AAAA,IAClB,cAAc;AAAA,IACd,aAAa;AAAA,EACf;AACA,MAAI,eAA4C;AAEhD,WAAS,aAAa;AAEpB,UAAM,aAAa,CAAC,GAAG,IAAI;AAI3B,QAAI,yDAAoB,QAAQ;AAC9B,cAAQ,cAAc;AAAA,IACxB;AAEA,UAAM,gBAAgB;AAAA;AAAA;AAAA,MAGpB,GAAG,gBAAgB,OAAO;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAM1B,YAAYH,YAAW;AAAA,IACzB;AAEA,WAAO;AAAA,MACL,SAAS;AAAA,MACT,MAAM;AAAA,MACN;AAAA,IACF,CAAC;AAAA,EACH;AAEA,QAAM,kBAAkB;AAAA,IACtB;AAAA,IACA,WAAW,OAAO;AAChB,UAAI,mBAAmB,MAAM;AAC3B,0BAAkB;AAAA,UAChB,MAAM;AAAA,UACN,MAAM;AAAA,QACR;AACA,gBAAQ,MAAM,KAAK,eAAe;AAAA,MACpC,OAAO;AACL,wBAAgB,QAAQ;AAAA,MAC1B;AAEA,cAAQ,WAAW;AACnB,iBAAW;AAAA,IACb;AAAA,IACA,gBAAgB,OAAO;AAzJ3B,UAAAE;AA0JM,UAAI,8BAA8B,MAAM;AACtC,qCAA6B,EAAE,MAAM,QAAQ,MAAM,MAAM;AACzD,YAAI,wBAAwB,MAAM;AAChC,+BAAqB,QAAQ,KAAK,0BAA0B;AAAA,QAC9D;AAAA,MACF,OAAO;AACL,mCAA2B,QAAQ;AAAA,MACrC;AAEA,UAAI,wBAAwB,MAAM;AAChC,+BAAuB;AAAA,UACrB,MAAM;AAAA,UACN,WAAW;AAAA,UACX,SAAS,CAAC,0BAA0B;AAAA,QACtC;AACA,gBAAQ,MAAM,KAAK,oBAAoB;AAAA,MACzC,OAAO;AACL,6BAAqB,aAAa;AAAA,MACpC;AAEA,cAAQ,cAAaA,MAAA,QAAQ,cAAR,OAAAA,MAAqB,MAAM;AAEhD,iBAAW;AAAA,IACb;AAAA,IACA,yBAAyB,OAAO;AAC9B,UAAI,8BAA8B,MAAM;AACtC,mCAA2B,YAAY,MAAM;AAAA,MAC/C;AAAA,IACF;AAAA,IACA,wBAAwB,OAAO;AAC7B,UAAI,wBAAwB,MAAM;AAChC,+BAAuB;AAAA,UACrB,MAAM;AAAA,UACN,WAAW;AAAA,UACX,SAAS,CAAC;AAAA,QACZ;AACA,gBAAQ,MAAM,KAAK,oBAAoB;AAAA,MACzC;AAEA,2BAAqB,QAAQ,KAAK;AAAA,QAChC,MAAM;AAAA,QACN,MAAM,MAAM;AAAA,MACd,CAAC;AAED,mCAA6B;AAE7B,iBAAW;AAAA,IACb;AAAA,IACA,WAAW,OAAO;AAChB,cAAQ,MAAM,KAAK;AAAA,QACjB,MAAM;AAAA,QACN,UAAU,MAAM;AAAA,QAChB,MAAM,MAAM;AAAA,MACd,CAAC;AAED,iBAAW;AAAA,IACb;AAAA,IACA,aAAa,OAAO;AAClB,cAAQ,MAAM,KAAK;AAAA,QACjB,MAAM;AAAA,QACN,QAAQ;AAAA,MACV,CAAC;AAED,iBAAW;AAAA,IACb;AAAA,IACA,6BAA6B,OAAO;AAClC,UAAI,QAAQ,mBAAmB,MAAM;AACnC,gBAAQ,kBAAkB,CAAC;AAAA,MAC7B;AAGA,uBAAiB,MAAM,UAAU,IAAI;AAAA,QACnC,MAAM;AAAA,QACN;AAAA,QACA,UAAU,MAAM;AAAA,QAChB,OAAO,QAAQ,gBAAgB;AAAA,MACjC;AAEA,YAAM,aAAa;AAAA,QACjB,OAAO;AAAA,QACP;AAAA,QACA,YAAY,MAAM;AAAA,QAClB,UAAU,MAAM;AAAA,QAChB,MAAM;AAAA,MACR;AAEA,cAAQ,gBAAgB,KAAK,UAAU;AAEvC,+BAAyB,MAAM,YAAY,UAAU;AAErD,iBAAW;AAAA,IACb;AAAA,IACA,oBAAoB,OAAO;AACzB,YAAM,kBAAkB,iBAAiB,MAAM,UAAU;AAEzD,sBAAgB,QAAQ,MAAM;AAE9B,YAAM,EAAE,OAAO,YAAY,IAAI,iBAAiB,gBAAgB,IAAI;AAEpE,YAAM,aAAa;AAAA,QACjB,OAAO;AAAA,QACP,MAAM,gBAAgB;AAAA,QACtB,YAAY,MAAM;AAAA,QAClB,UAAU,gBAAgB;AAAA,QAC1B,MAAM;AAAA,MACR;AAEA,cAAQ,gBAAiB,gBAAgB,KAAK,IAAI;AAElD,+BAAyB,MAAM,YAAY,UAAU;AAErD,iBAAW;AAAA,IACb;AAAA,IACA,MAAM,eAAe,OAAO;AAC1B,YAAM,aAAa;AAAA,QACjB,OAAO;AAAA,QACP;AAAA,QACA,GAAG;AAAA,MACL;AAEA,UAAI,iBAAiB,MAAM,UAAU,KAAK,MAAM;AAE9C,gBAAQ,gBAAiB,iBAAiB,MAAM,UAAU,EAAE,KAAK,IAC/D;AAAA,MACJ,OAAO;AACL,YAAI,QAAQ,mBAAmB,MAAM;AACnC,kBAAQ,kBAAkB,CAAC;AAAA,QAC7B;AAEA,gBAAQ,gBAAgB,KAAK,UAAU;AAAA,MACzC;AAEA,+BAAyB,MAAM,YAAY,UAAU;AAErD,iBAAW;AAKX,UAAI,YAAY;AACd,cAAM,SAAS,MAAM,WAAW,EAAE,UAAU,MAAM,CAAC;AACnD,YAAI,UAAU,MAAM;AAClB,gBAAME,cAAa;AAAA,YACjB,OAAO;AAAA,YACP;AAAA,YACA,GAAG;AAAA,YACH;AAAA,UACF;AAGA,kBAAQ,gBAAiB,QAAQ,gBAAiB,SAAS,CAAC,IAC1DA;AAEF,mCAAyB,MAAM,YAAYA,WAAU;AAErD,qBAAW;AAAA,QACb;AAAA,MACF;AAAA,IACF;AAAA,IACA,iBAAiB,OAAO;AACtB,YAAM,kBAAkB,QAAQ;AAEhC,UAAI,mBAAmB,MAAM;AAC3B,cAAM,IAAI,MAAM,6CAA6C;AAAA,MAC/D;AAIA,YAAM,sBAAsB,gBAAgB;AAAA,QAC1C,CAAAA,gBAAcA,YAAW,eAAe,MAAM;AAAA,MAChD;AAEA,UAAI,wBAAwB,IAAI;AAC9B,cAAM,IAAI;AAAA,UACR;AAAA,QACF;AAAA,MACF;AAEA,YAAM,aAAa;AAAA,QACjB,GAAG,gBAAgB,mBAAmB;AAAA,QACtC,OAAO;AAAA,QACP,GAAG;AAAA,MACL;AAEA,sBAAgB,mBAAmB,IAAI;AAEvC,+BAAyB,MAAM,YAAY,UAAU;AAErD,iBAAW;AAAA,IACb;AAAA,IACA,WAAW,OAAO;AAChB,WAAK,KAAK,GAAG,KAAK;AAClB,iBAAW;AAAA,IACb;AAAA,IACA,yBAAyB,OAAO;AAC9B,UAAI,sBAAsB,MAAM;AAC9B,6BAAqB,CAAC,GAAG,KAAK;AAAA,MAChC,OAAO;AACL,2BAAmB,KAAK,GAAG,KAAK;AAAA,MAClC;AAEA,iBAAW;AAAA,IACb;AAAA,IACA,iBAAiB,OAAO;AACtB,cAAQ;AAGR,wBAAkB,MAAM,cAAc,kBAAkB;AACxD,6BAAuB;AACvB,mCAA6B;AAAA,IAC/B;AAAA,IACA,gBAAgB,OAAO;AAErB,UAAI,CAAC,oBAAoB;AACvB,gBAAQ,KAAK,MAAM;AAAA,MACrB;AAGA,cAAQ,MAAM,KAAK,EAAE,MAAM,aAAa,CAAC;AACzC,iBAAW;AAAA,IACb;AAAA,IACA,oBAAoB,OAAO;AACzB,qBAAe,MAAM;AACrB,UAAI,MAAM,SAAS,MAAM;AACvB,gBAAQ,4BAA4B,MAAM,KAAK;AAAA,MACjD;AAAA,IACF;AAAA,IACA,YAAY,OAAO;AACjB,YAAM,IAAI,MAAM,KAAK;AAAA,IACvB;AAAA,EACF,CAAC;AAED,uCAAW,EAAE,SAAS,cAAc,MAAM;AAC5C;;;AMlYA,IAAAC,yBAAiD;;;ACDjD,eAAsB,kBAAkB;AAAA,EACtC;AAAA,EACA;AACF,GAGkB;AAChB,QAAM,SAAS,OAAO,YAAY,IAAI,kBAAkB,CAAC,EAAE,UAAU;AACrE,SAAO,MAAM;AACX,UAAM,EAAE,MAAM,MAAM,IAAI,MAAM,OAAO,KAAK;AAC1C,QAAI,MAAM;AACR;AAAA,IACF;AACA,UAAM,WAAW,KAAK;AAAA,EACxB;AACF;;;ADVA,eAAsB,wBAAwB;AAAA,EAC5C;AAAA,EACA;AAAA,EACA;AAAA,EACA,iBAAiB,MAAM,oBAAI,KAAK;AAAA,EAChC,YAAAC,cAAa,uBAAAC;AACf,GAUG;AACD,QAAM,WAAuB,EAAE,MAAM,QAAQ,MAAM,GAAG;AAEtD,QAAM,gBAA2B;AAAA,IAC/B,IAAID,YAAW;AAAA,IACf,WAAW,eAAe;AAAA,IAC1B,MAAM;AAAA,IACN,SAAS;AAAA,IACT,OAAO,CAAC,QAAQ;AAAA,EAClB;AAEA,QAAM,kBAAkB;AAAA,IACtB;AAAA,IACA,YAAY,WAAS;AACnB,oBAAc,WAAW;AACzB,eAAS,QAAQ;AAGjB,aAAO;AAAA,QACL,SAAS,EAAE,GAAG,cAAc;AAAA,QAC5B,MAAM,CAAC;AAAA,QACP,oBAAoB;AAAA,MACtB,CAAC;AAAA,IACH;AAAA,EACF,CAAC;AAGD,uCAAW,eAAe;AAAA,IACxB,OAAO,EAAE,kBAAkB,KAAK,cAAc,KAAK,aAAa,IAAI;AAAA,IACpE,cAAc;AAAA,EAChB;AACF;;;AE/CA,IAAM,mBAAmB,MAAM;AAE/B,eAAsB,YAAY;AAAA,EAChC;AAAA,EACA;AAAA,EACA,iBAAiB;AAAA,EACjB;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA,YAAAE;AAAA,EACA,OAAAC,SAAQ,iBAAiB;AAAA,EACzB;AAAA,EACA,cAAc;AAChB,GAoBG;AA3CH;AA4CE,QAAM,UACJ,gBAAgB,WACZA,OAAM,GAAG,GAAG,WAAW,KAAK,EAAE,IAAI;AAAA,IAChC,QAAQ;AAAA,IACR,SAAS;AAAA,MACP,gBAAgB;AAAA,MAChB,GAAG;AAAA,IACL;AAAA,IACA,SAAQ,iFAAqB;AAAA,IAC7B;AAAA,EACF,CAAC,IACDA,OAAM,KAAK;AAAA,IACT,QAAQ;AAAA,IACR,MAAM,KAAK,UAAU,IAAI;AAAA,IACzB,SAAS;AAAA,MACP,gBAAgB;AAAA,MAChB,GAAG;AAAA,IACL;AAAA,IACA,SAAQ,iFAAqB;AAAA,IAC7B;AAAA,EACF,CAAC;AAEP,QAAM,WAAW,MAAM,QAAQ,MAAM,SAAO;AAC1C,6BAAyB;AACzB,UAAM;AAAA,EACR,CAAC;AAED,MAAI,YAAY;AACd,QAAI;AACF,YAAM,WAAW,QAAQ;AAAA,IAC3B,SAAS,KAAK;AACZ,YAAM;AAAA,IACR;AAAA,EACF;AAEA,MAAI,CAAC,SAAS,IAAI;AAChB,6BAAyB;AACzB,UAAM,IAAI;AAAA,OACP,WAAM,SAAS,KAAK,MAApB,YAA0B;AAAA,IAC7B;AAAA,EACF;AAEA,MAAI,CAAC,SAAS,MAAM;AAClB,UAAM,IAAI,MAAM,6BAA6B;AAAA,EAC/C;AAEA,UAAQ,gBAAgB;AAAA,IACtB,KAAK,QAAQ;AACX,YAAM,wBAAwB;AAAA,QAC5B,QAAQ,SAAS;AAAA,QACjB,QAAQ;AAAA,QACR;AAAA,QACA,YAAAD;AAAA,MACF,CAAC;AACD;AAAA,IACF;AAAA,IAEA,KAAK,QAAQ;AACX,YAAM,oBAAoB;AAAA,QACxB,QAAQ,SAAS;AAAA,QACjB,QAAQ;AAAA,QACR;AAAA,QACA;AAAA,QACA,SAAS,EAAE,SAAS,cAAc,MAAM,GAAG;AACzC,cAAI,YAAY,WAAW,MAAM;AAC/B,qBAAS,SAAS,EAAE,OAAO,aAAa,CAAC;AAAA,UAC3C;AAAA,QACF;AAAA,QACA,YAAAA;AAAA,MACF,CAAC;AACD;AAAA,IACF;AAAA,IAEA,SAAS;AACP,YAAM,kBAAyB;AAC/B,YAAM,IAAI,MAAM,4BAA4B,eAAe,EAAE;AAAA,IAC/D;AAAA,EACF;AACF;;;ACrHA,IAAME,oBAAmB,MAAM;AAE/B,eAAsB,kBAAkB;AAAA,EACtC;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA,iBAAiB;AAAA,EACjB;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA,OAAAC,SAAQD,kBAAiB;AAC3B,GAgBG;AAvCH;AAwCE,MAAI;AACF,eAAW,IAAI;AACf,aAAS,MAAS;AAElB,UAAM,kBAAkB,IAAI,gBAAgB;AAC5C,uBAAmB,eAAe;AAGlC,kBAAc,EAAE;AAEhB,UAAM,WAAW,MAAMC,OAAM,KAAK;AAAA,MAChC,QAAQ;AAAA,MACR,MAAM,KAAK,UAAU;AAAA,QACnB;AAAA,QACA,GAAG;AAAA,MACL,CAAC;AAAA,MACD;AAAA,MACA,SAAS;AAAA,QACP,gBAAgB;AAAA,QAChB,GAAG;AAAA,MACL;AAAA,MACA,QAAQ,gBAAgB;AAAA,IAC1B,CAAC,EAAE,MAAM,SAAO;AACd,YAAM;AAAA,IACR,CAAC;AAED,QAAI,YAAY;AACd,UAAI;AACF,cAAM,WAAW,QAAQ;AAAA,MAC3B,SAAS,KAAK;AACZ,cAAM;AAAA,MACR;AAAA,IACF;AAEA,QAAI,CAAC,SAAS,IAAI;AAChB,YAAM,IAAI;AAAA,SACP,WAAM,SAAS,KAAK,MAApB,YAA0B;AAAA,MAC7B;AAAA,IACF;AAEA,QAAI,CAAC,SAAS,MAAM;AAClB,YAAM,IAAI,MAAM,6BAA6B;AAAA,IAC/C;AAEA,QAAI,SAAS;AAEb,YAAQ,gBAAgB;AAAA,MACtB,KAAK,QAAQ;AACX,cAAM,kBAAkB;AAAA,UACtB,QAAQ,SAAS;AAAA,UACjB,YAAY,WAAS;AACnB,sBAAU;AACV,0BAAc,MAAM;AAAA,UACtB;AAAA,QACF,CAAC;AACD;AAAA,MACF;AAAA,MACA,KAAK,QAAQ;AACX,cAAM,kBAAkB;AAAA,UACtB,QAAQ,SAAS;AAAA,UACjB,WAAW,OAAO;AAChB,sBAAU;AACV,0BAAc,MAAM;AAAA,UACtB;AAAA,UACA,WAAW,OAAO;AAChB,6CAAS;AAAA,UACX;AAAA,UACA,YAAY,OAAO;AACjB,kBAAM,IAAI,MAAM,KAAK;AAAA,UACvB;AAAA,QACF,CAAC;AACD;AAAA,MACF;AAAA,MACA,SAAS;AACP,cAAM,kBAAyB;AAC/B,cAAM,IAAI,MAAM,4BAA4B,eAAe,EAAE;AAAA,MAC/D;AAAA,IACF;AAEA,QAAI,UAAU;AACZ,eAAS,QAAQ,MAAM;AAAA,IACzB;AAEA,uBAAmB,IAAI;AACvB,WAAO;AAAA,EACT,SAAS,KAAK;AAEZ,QAAK,IAAY,SAAS,cAAc;AACtC,yBAAmB,IAAI;AACvB,aAAO;AAAA,IACT;AAEA,QAAI,eAAe,OAAO;AACxB,UAAI,SAAS;AACX,gBAAQ,GAAG;AAAA,MACb;AAAA,IACF;AAEA,aAAS,GAAY;AAAA,EACvB,UAAE;AACA,eAAW,KAAK;AAAA,EAClB;AACF;;;AC3IO,SAAS,mBAAmB,SAAyB;AAC1D,QAAM,CAAC,QAAQ,aAAa,IAAI,QAAQ,MAAM,GAAG;AACjD,QAAM,WAAW,OAAO,MAAM,GAAG,EAAE,CAAC,EAAE,MAAM,GAAG,EAAE,CAAC;AAElD,MAAI,YAAY,QAAQ,iBAAiB,MAAM;AAC7C,UAAM,IAAI,MAAM,yBAAyB;AAAA,EAC3C;AAEA,MAAI;AACF,WAAO,OAAO,KAAK,aAAa;AAAA,EAClC,SAAS,OAAO;AACd,UAAM,IAAI,MAAM,yBAAyB;AAAA,EAC3C;AACF;;;ACdO,SAAS,6BACd,iBACoB;AACpB,SAAO,mDAAiB,OAAO,CAAC,KAAK,mBAAmB;AAL1D;AAMI,WAAO,KAAK,IAAI,MAAK,oBAAe,SAAf,YAAuB,CAAC;AAAA,EAC/C,GAAG;AACL;;;ACIO,SAAS,gBACd,SAQE;AArBJ;AAsBE,UACE,aAAQ,UAAR,YAAiB;AAAA,IACf,GAAI,QAAQ,kBACR,QAAQ,gBAAgB,IAAI,qBAAmB;AAAA,MAC7C,MAAM;AAAA,MACN;AAAA,IACF,EAAE,IACF,CAAC;AAAA,IACL,GAAI,QAAQ,YACR;AAAA,MACE;AAAA,QACE,MAAM;AAAA,QACN,WAAW,QAAQ;AAAA,QACnB,SAAS,CAAC,EAAE,MAAM,QAAiB,MAAM,QAAQ,UAAU,CAAC;AAAA,MAC9D;AAAA,IACF,IACA,CAAC;AAAA,IACL,GAAI,QAAQ,UACR,CAAC,EAAE,MAAM,QAAiB,MAAM,QAAQ,QAAQ,CAAC,IACjD,CAAC;AAAA,EACP;AAEJ;;;ACzCO,SAAS,iBAAiB,UAAkC;AACjE,SAAO,SAAS,IAAI,cAAY;AAAA,IAC9B,GAAG;AAAA,IACH,OAAO,gBAAgB,OAAO;AAAA,EAChC,EAAE;AACJ;;;ACDO,SAAS,gBAAgB,MAAW,MAAoB;AAE7D,MAAI,SAAS;AAAM,WAAO;AAG1B,MAAI,QAAQ,QAAQ,QAAQ;AAAM,WAAO;AAGzC,MAAI,OAAO,SAAS,YAAY,OAAO,SAAS;AAC9C,WAAO,SAAS;AAGlB,MAAI,KAAK,gBAAgB,KAAK;AAAa,WAAO;AAGlD,MAAI,gBAAgB,QAAQ,gBAAgB,MAAM;AAChD,WAAO,KAAK,QAAQ,MAAM,KAAK,QAAQ;AAAA,EACzC;AAGA,MAAI,MAAM,QAAQ,IAAI,GAAG;AACvB,QAAI,KAAK,WAAW,KAAK;AAAQ,aAAO;AACxC,aAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,KAAK;AACpC,UAAI,CAAC,gBAAgB,KAAK,CAAC,GAAG,KAAK,CAAC,CAAC;AAAG,eAAO;AAAA,IACjD;AACA,WAAO;AAAA,EACT;AAGA,QAAM,QAAQ,OAAO,KAAK,IAAI;AAC9B,QAAM,QAAQ,OAAO,KAAK,IAAI;AAC9B,MAAI,MAAM,WAAW,MAAM;AAAQ,WAAO;AAG1C,aAAW,OAAO,OAAO;AACvB,QAAI,CAAC,MAAM,SAAS,GAAG;AAAG,aAAO;AACjC,QAAI,CAAC,gBAAgB,KAAK,GAAG,GAAG,KAAK,GAAG,CAAC;AAAG,aAAO;AAAA,EACrD;AAEA,SAAO;AACT;;;AC7CA,eAAsB,6BACpB,wBACA;AACA,MAAI,CAAC,wBAAwB;AAC3B,WAAO,CAAC;AAAA,EACV;AAKA,MACE,WAAW,YACX,kCAAkC,WAAW,UAC7C;AACA,WAAO,QAAQ;AAAA,MACb,MAAM,KAAK,sBAAsB,EAAE,IAAI,OAAM,eAAc;AACzD,cAAM,EAAE,MAAM,KAAK,IAAI;AAEvB,cAAM,UAAU,MAAM,IAAI,QAAgB,CAAC,SAAS,WAAW;AAC7D,gBAAM,SAAS,IAAI,WAAW;AAC9B,iBAAO,SAAS,iBAAe;AAtBzC;AAuBY,qBAAQ,iBAAY,WAAZ,mBAAoB,MAAgB;AAAA,UAC9C;AACA,iBAAO,UAAU,WAAS,OAAO,KAAK;AACtC,iBAAO,cAAc,UAAU;AAAA,QACjC,CAAC;AAED,eAAO;AAAA,UACL;AAAA,UACA,aAAa;AAAA,UACb,KAAK;AAAA,QACP;AAAA,MACF,CAAC;AAAA,IACH;AAAA,EACF;AAEA,MAAI,MAAM,QAAQ,sBAAsB,GAAG;AACzC,WAAO;AAAA,EACT;AAEA,QAAM,IAAI,MAAM,0BAA0B;AAC5C;;;ACtCA,IAAMC,WAAU,KAAK,WAAW,CAAC;AAGjC,SAASC,cAAa,QAAsB,aAAqB;AAC/D,QAAM,qBAAqB,IAAI,WAAW,WAAW;AAErD,MAAI,SAAS;AACb,aAAW,SAAS,QAAQ;AAC1B,uBAAmB,IAAI,OAAO,MAAM;AACpC,cAAU,MAAM;AAAA,EAClB;AACA,SAAO,SAAS;AAEhB,SAAO;AACT;AAEA,eAAsB,uBAAuB;AAAA,EAC3C;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF,GAqBkB;AAIhB,QAAM,SAAS,OAAO,UAAU;AAChC,QAAM,UAAU,IAAI,YAAY;AAChC,QAAM,SAAuB,CAAC;AAC9B,MAAI,cAAc;AAElB,SAAO,MAAM;AACX,UAAM,EAAE,MAAM,IAAI,MAAM,OAAO,KAAK;AAEpC,QAAI,OAAO;AACT,aAAO,KAAK,KAAK;AACjB,qBAAe,MAAM;AACrB,UAAI,MAAM,MAAM,SAAS,CAAC,MAAMD,UAAS;AAEvC;AAAA,MACF;AAAA,IACF;AAEA,QAAI,OAAO,WAAW,GAAG;AACvB;AAAA,IACF;AAEA,UAAM,qBAAqBC,cAAa,QAAQ,WAAW;AAC3D,kBAAc;AAEd,UAAM,cAAc,QACjB,OAAO,oBAAoB,EAAE,QAAQ,KAAK,CAAC,EAC3C,MAAM,IAAI,EACV,OAAO,UAAQ,SAAS,EAAE,EAC1B,IAAI,wBAAwB;AAE/B,eAAW,EAAE,MAAM,OAAAC,OAAM,KAAK,aAAa;AACzC,cAAQ,MAAM;AAAA,QACZ,KAAK;AACH,iBAAM,yCAAaA;AACnB;AAAA,QACF,KAAK;AACH,iBAAM,2CAAcA;AACpB;AAAA,QACF,KAAK;AACH,iBAAM,iEAAyBA;AAC/B;AAAA,QACF,KAAK;AACH,iBAAM,yEAA6BA;AACnC;AAAA,QACF,KAAK;AACH,iBAAM,uDAAoBA;AAC1B;AAAA,QACF,SAAS;AACP,gBAAM,kBAAyB;AAC/B,gBAAM,IAAI,MAAM,6BAA6B,eAAe,EAAE;AAAA,QAChE;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACF;;;AC3GA,IAAAC,yBAA2C;;;ACE3C,gCAA4B;AAGrB,SAAS,UACdC,YACA,SASgB;AAhBlB;AAkBE,QAAM,iBAAgB,wCAAS,kBAAT,YAA0B;AAEhD,SAAO;AAAA,QACL,0BAAAC,SAAgBD,YAAW;AAAA,MACzB,cAAc,gBAAgB,SAAS;AAAA,MACvC,QAAQ;AAAA;AAAA,IACV,CAAC;AAAA,IACD;AAAA,MACE,UAAU,WAAS;AACjB,cAAM,SAASA,WAAU,UAAU,KAAK;AACxC,eAAO,OAAO,UACV,EAAE,SAAS,MAAM,OAAO,OAAO,KAAK,IACpC,EAAE,SAAS,OAAO,OAAO,OAAO,MAAM;AAAA,MAC5C;AAAA,IACF;AAAA,EACF;AACF;;;AD1BA,IAAM,eAAe,OAAO,IAAI,kBAAkB;AAyB3C,SAAS,WACdE,aACA;AAAA,EACE;AACF,IAII,CAAC,GACW;AAChB,SAAO;AAAA,IACL,CAAC,YAAY,GAAG;AAAA,IAChB,OAAO;AAAA;AAAA,IACP,CAAC,sCAAe,GAAG;AAAA,IACnB,YAAAA;AAAA,IACA;AAAA,EACF;AACF;AAEA,SAAS,SAAS,OAAiC;AACjD,SACE,OAAO,UAAU,YACjB,UAAU,QACV,gBAAgB,SAChB,MAAM,YAAY,MAAM,QACxB,gBAAgB,SAChB,cAAc;AAElB;AAEO,SAAS,SACd,QACgB;AAChB,SAAO,SAAS,MAAM,IAAI,SAAS,UAAU,MAAM;AACrD;;;AEhEO,SAAS,uBAAuB;AAAA,EACrC;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF,GAKG;AAbH;AAcE,QAAM,cAAc,SAAS,SAAS,SAAS,CAAC;AAChD;AAAA;AAAA,IAEE,WAAW;AAAA,IAEX,eAAe;AAAA,KAEd,SAAS,SAAS,wBACjB,6BAA6B,YAAY,eAAe,MACtD;AAAA,IAEJ,yCAAyC,WAAW;AAAA,MAEnD,kCAA6B,YAAY,eAAe,MAAxD,YAA6D,KAAK;AAAA;AAEvE;AAOO,SAAS,yCACd,SAGA;AACA,MAAI,QAAQ,SAAS,aAAa;AAChC,WAAO;AAAA,EACT;AAEA,QAAM,qBAAqB,QAAQ,MAAM,OAAO,CAAC,WAAW,MAAM,UAAU;AAC1E,WAAO,KAAK,SAAS,eAAe,QAAQ;AAAA,EAC9C,GAAG,EAAE;AAEL,QAAM,0BAA0B,QAAQ,MACrC,MAAM,qBAAqB,CAAC,EAC5B,OAAO,UAAQ,KAAK,SAAS,iBAAiB;AAEjD,SACE,wBAAwB,SAAS,KACjC,wBAAwB,MAAM,UAAQ,YAAY,KAAK,cAAc;AAEzE;;;AC9CO,SAAS,qBAAqB;AAAA,EACnC;AAAA,EACA;AAAA,EACA,YAAY;AACd,GAIG;AAnBH;AAoBE,QAAM,cAAc,SAAS,SAAS,SAAS,CAAC;AAEhD,QAAM,iBAAiB,YAAY,MAAM;AAAA,IACvC,CAAC,SACC,KAAK,SAAS,qBACd,KAAK,eAAe,eAAe;AAAA,EACvC;AAEA,MAAI,kBAAkB,MAAM;AAC1B;AAAA,EACF;AAEA,QAAM,aAAa;AAAA,IACjB,GAAG,eAAe;AAAA,IAClB,OAAO;AAAA,IACP;AAAA,EACF;AAEA,iBAAe,iBAAiB;AAEhC,cAAY,mBAAkB,iBAAY,oBAAZ,mBAA6B;AAAA,IACzD,oBACE,eAAe,eAAe,aAAa,aAAa;AAAA;AAE9D;", "names": ["import_provider_utils", "import_provider_utils", "textStreamPart", "errorStreamPart", "validCodes", "value", "generateId", "generateIdFunction", "_a", "part", "invocation", "import_provider_utils", "generateId", "generateIdFunction", "generateId", "fetch", "getOriginalFetch", "fetch", "NEWLINE", "concatChunks", "value", "import_provider_utils", "zodSchema", "zodToJsonSchema", "jsonSchema"]}