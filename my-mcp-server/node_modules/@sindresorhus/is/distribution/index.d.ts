import type { ArrayLike, Class, Falsy, NodeStream, NonEmptyString, ObservableLike, Predicate, Primitive, TypedArray, WeakRef, Whitespace } from './types.js';
type ExtractFromGlobalConstructors<Name extends string> = Name extends string ? typeof globalThis extends Record<Name, new (...arguments_: any[]) => infer T> ? T : never : never;
type NodeBuffer = ExtractFromGlobalConstructors<'Buffer'>;
declare const objectTypeNames: readonly ["Function", "Generator", "AsyncGenerator", "GeneratorFunction", "AsyncGeneratorFunction", "AsyncFunction", "Observable", "Array", "Buffer", "Blob", "Object", "RegExp", "Date", "Error", "Map", "Set", "WeakMap", "WeakSet", "WeakRef", "ArrayBuffer", "SharedArrayBuffer", "DataView", "Promise", "URL", "FormData", "URLSearchParams", "HTMLElement", "NaN", "Int8Array", "Uint8Array", "Uint8ClampedArray", "Int16Array", "Uint16Array", "Int32Array", "Uint32Array", "Float32Array", "Float64Array", "BigInt64Array", "BigUint64Array"];
type ObjectTypeName = typeof objectTypeNames[number];
declare const primitiveTypeNames: readonly ["null", "undefined", "string", "number", "bigint", "boolean", "symbol"];
type PrimitiveTypeName = typeof primitiveTypeNames[number];
export type TypeName = ObjectTypeName | PrimitiveTypeName;
declare const assertionTypeDescriptions: readonly ["positive number", "negative number", "Class", "string with a number", "null or undefined", "Iterable", "AsyncIterable", "native Promise", "EnumCase", "string with a URL", "truthy", "falsy", "primitive", "integer", "plain object", "TypedArray", "array-like", "tuple-like", "Node.js Stream", "infinite number", "empty array", "non-empty array", "empty string", "empty string or whitespace", "non-empty string", "non-empty string and not whitespace", "empty object", "non-empty object", "empty set", "non-empty set", "empty map", "non-empty map", "PropertyKey", "even integer", "odd integer", "T", "in range", "predicate returns truthy for any value", "predicate returns truthy for all values", "valid Date", "valid length", "whitespace string", "Function", "Generator", "AsyncGenerator", "GeneratorFunction", "AsyncGeneratorFunction", "AsyncFunction", "Observable", "Array", "Buffer", "Blob", "Object", "RegExp", "Date", "Error", "Map", "Set", "WeakMap", "WeakSet", "WeakRef", "ArrayBuffer", "SharedArrayBuffer", "DataView", "Promise", "URL", "FormData", "URLSearchParams", "HTMLElement", "NaN", "Int8Array", "Uint8Array", "Uint8ClampedArray", "Int16Array", "Uint16Array", "Int32Array", "Uint32Array", "Float32Array", "Float64Array", "BigInt64Array", "BigUint64Array", "null", "undefined", "string", "number", "bigint", "boolean", "symbol"];
export type AssertionTypeDescription = typeof assertionTypeDescriptions[number];
declare function detect(value: unknown): TypeName;
declare const is: typeof detect & {
    all: typeof isAll;
    any: typeof isAny;
    array: typeof isArray;
    arrayBuffer: typeof isArrayBuffer;
    arrayLike: typeof isArrayLike;
    asyncFunction: typeof isAsyncFunction;
    asyncGenerator: typeof isAsyncGenerator;
    asyncGeneratorFunction: typeof isAsyncGeneratorFunction;
    asyncIterable: typeof isAsyncIterable;
    bigint: typeof isBigint;
    bigInt64Array: typeof isBigInt64Array;
    bigUint64Array: typeof isBigUint64Array;
    blob: typeof isBlob;
    boolean: typeof isBoolean;
    boundFunction: typeof isBoundFunction;
    buffer: typeof isBuffer;
    class: typeof isClass;
    dataView: typeof isDataView;
    date: typeof isDate;
    detect: typeof detect;
    directInstanceOf: typeof isDirectInstanceOf;
    emptyArray: typeof isEmptyArray;
    emptyMap: typeof isEmptyMap;
    emptyObject: typeof isEmptyObject;
    emptySet: typeof isEmptySet;
    emptyString: typeof isEmptyString;
    emptyStringOrWhitespace: typeof isEmptyStringOrWhitespace;
    enumCase: typeof isEnumCase;
    error: typeof isError;
    evenInteger: typeof isEvenInteger;
    falsy: typeof isFalsy;
    float32Array: typeof isFloat32Array;
    float64Array: typeof isFloat64Array;
    formData: typeof isFormData;
    function: typeof isFunction;
    generator: typeof isGenerator;
    generatorFunction: typeof isGeneratorFunction;
    htmlElement: typeof isHtmlElement;
    infinite: typeof isInfinite;
    inRange: typeof isInRange;
    int16Array: typeof isInt16Array;
    int32Array: typeof isInt32Array;
    int8Array: typeof isInt8Array;
    integer: typeof isInteger;
    iterable: typeof isIterable;
    map: typeof isMap;
    nan: typeof isNan;
    nativePromise: typeof isNativePromise;
    negativeNumber: typeof isNegativeNumber;
    nodeStream: typeof isNodeStream;
    nonEmptyArray: typeof isNonEmptyArray;
    nonEmptyMap: typeof isNonEmptyMap;
    nonEmptyObject: typeof isNonEmptyObject;
    nonEmptySet: typeof isNonEmptySet;
    nonEmptyString: typeof isNonEmptyString;
    nonEmptyStringAndNotWhitespace: typeof isNonEmptyStringAndNotWhitespace;
    null: typeof isNull;
    nullOrUndefined: typeof isNullOrUndefined;
    number: typeof isNumber;
    numericString: typeof isNumericString;
    object: typeof isObject;
    observable: typeof isObservable;
    oddInteger: typeof isOddInteger;
    plainObject: typeof isPlainObject;
    positiveNumber: typeof isPositiveNumber;
    primitive: typeof isPrimitive;
    promise: typeof isPromise;
    propertyKey: typeof isPropertyKey;
    regExp: typeof isRegExp;
    safeInteger: typeof isSafeInteger;
    set: typeof isSet;
    sharedArrayBuffer: typeof isSharedArrayBuffer;
    string: typeof isString;
    symbol: typeof isSymbol;
    truthy: typeof isTruthy;
    tupleLike: typeof isTupleLike;
    typedArray: typeof isTypedArray;
    uint16Array: typeof isUint16Array;
    uint32Array: typeof isUint32Array;
    uint8Array: typeof isUint8Array;
    uint8ClampedArray: typeof isUint8ClampedArray;
    undefined: typeof isUndefined;
    urlInstance: typeof isUrlInstance;
    urlSearchParams: typeof isUrlSearchParams;
    urlString: typeof isUrlString;
    validDate: typeof isValidDate;
    validLength: typeof isValidLength;
    weakMap: typeof isWeakMap;
    weakRef: typeof isWeakRef;
    weakSet: typeof isWeakSet;
    whitespaceString: typeof isWhitespaceString;
};
export declare function isAll(predicate: Predicate, ...values: unknown[]): boolean;
export declare function isAny(predicate: Predicate | Predicate[], ...values: unknown[]): boolean;
export declare function isArray<T = unknown>(value: unknown, assertion?: (value: T) => value is T): value is T[];
export declare function isArrayBuffer(value: unknown): value is ArrayBuffer;
export declare function isArrayLike<T = unknown>(value: unknown): value is ArrayLike<T>;
export declare function isAsyncFunction<T = unknown>(value: unknown): value is ((...arguments_: any[]) => Promise<T>);
export declare function isAsyncGenerator(value: unknown): value is AsyncGenerator;
export declare function isAsyncGeneratorFunction(value: unknown): value is ((...arguments_: any[]) => Promise<unknown>);
export declare function isAsyncIterable<T = unknown>(value: unknown): value is AsyncIterable<T>;
export declare function isBigint(value: unknown): value is bigint;
export declare function isBigInt64Array(value: unknown): value is BigInt64Array;
export declare function isBigUint64Array(value: unknown): value is BigUint64Array;
export declare function isBlob(value: unknown): value is Blob;
export declare function isBoolean(value: unknown): value is boolean;
export declare function isBoundFunction(value: unknown): value is Function;
/**
Note: [Prefer using `Uint8Array` instead of `Buffer`.](https://sindresorhus.com/blog/goodbye-nodejs-buffer)
*/
export declare function isBuffer(value: unknown): value is NodeBuffer;
export declare function isClass<T = unknown>(value: unknown): value is Class<T>;
export declare function isDataView(value: unknown): value is DataView;
export declare function isDate(value: unknown): value is Date;
export declare function isDirectInstanceOf<T>(instance: unknown, class_: Class<T>): instance is T;
export declare function isEmptyArray(value: unknown): value is never[];
export declare function isEmptyMap(value: unknown): value is Map<never, never>;
export declare function isEmptyObject<Key extends keyof any = string>(value: unknown): value is Record<Key, never>;
export declare function isEmptySet(value: unknown): value is Set<never>;
export declare function isEmptyString(value: unknown): value is '';
export declare function isEmptyStringOrWhitespace(value: unknown): value is '' | Whitespace;
export declare function isEnumCase<T = unknown>(value: unknown, targetEnum: T): value is T[keyof T];
export declare function isError(value: unknown): value is Error;
export declare function isEvenInteger(value: unknown): value is number;
export declare function isFalsy(value: unknown): value is Falsy;
export declare function isFloat32Array(value: unknown): value is Float32Array;
export declare function isFloat64Array(value: unknown): value is Float64Array;
export declare function isFormData(value: unknown): value is FormData;
export declare function isFunction(value: unknown): value is Function;
export declare function isGenerator(value: unknown): value is Generator;
export declare function isGeneratorFunction(value: unknown): value is GeneratorFunction;
export declare function isHtmlElement(value: unknown): value is HTMLElement;
export declare function isInfinite(value: unknown): value is number;
export declare function isInRange(value: number, range: number | [number, number]): value is number;
export declare function isInt16Array(value: unknown): value is Int16Array;
export declare function isInt32Array(value: unknown): value is Int32Array;
export declare function isInt8Array(value: unknown): value is Int8Array;
export declare function isInteger(value: unknown): value is number;
export declare function isIterable<T = unknown>(value: unknown): value is Iterable<T>;
export declare function isMap<Key = unknown, Value = unknown>(value: unknown): value is Map<Key, Value>;
export declare function isNan(value: unknown): boolean;
export declare function isNativePromise<T = unknown>(value: unknown): value is Promise<T>;
export declare function isNegativeNumber(value: unknown): value is number;
export declare function isNodeStream(value: unknown): value is NodeStream;
export declare function isNonEmptyArray<T = unknown, Item = unknown>(value: T | Item[]): value is [Item, ...Item[]];
export declare function isNonEmptyMap<Key = unknown, Value = unknown>(value: unknown): value is Map<Key, Value>;
export declare function isNonEmptyObject<Key extends keyof any = string, Value = unknown>(value: unknown): value is Record<Key, Value>;
export declare function isNonEmptySet<T = unknown>(value: unknown): value is Set<T>;
export declare function isNonEmptyString(value: unknown): value is NonEmptyString;
export declare function isNonEmptyStringAndNotWhitespace(value: unknown): value is NonEmptyString;
export declare function isNull(value: unknown): value is null;
export declare function isNullOrUndefined(value: unknown): value is null | undefined;
export declare function isNumber(value: unknown): value is number;
export declare function isNumericString(value: unknown): value is `${number}`;
export declare function isObject(value: unknown): value is object;
export declare function isObservable(value: unknown): value is ObservableLike;
export declare function isOddInteger(value: unknown): value is number;
export declare function isPlainObject<Value = unknown>(value: unknown): value is Record<PropertyKey, Value>;
export declare function isPositiveNumber(value: unknown): value is number;
export declare function isPrimitive(value: unknown): value is Primitive;
export declare function isPromise<T = unknown>(value: unknown): value is Promise<T>;
export declare function isPropertyKey(value: unknown): value is PropertyKey;
export declare function isRegExp(value: unknown): value is RegExp;
export declare function isSafeInteger(value: unknown): value is number;
export declare function isSet<T = unknown>(value: unknown): value is Set<T>;
export declare function isSharedArrayBuffer(value: unknown): value is SharedArrayBuffer;
export declare function isString(value: unknown): value is string;
export declare function isSymbol(value: unknown): value is symbol;
export declare function isTruthy<T>(value: T | Falsy): value is T;
type TypeGuard<T> = (value: unknown) => value is T;
type ResolveTypesOfTypeGuardsTuple<TypeGuardsOfT, ResultOfT extends unknown[] = []> = TypeGuardsOfT extends [TypeGuard<infer U>, ...infer TOthers] ? ResolveTypesOfTypeGuardsTuple<TOthers, [...ResultOfT, U]> : TypeGuardsOfT extends undefined[] ? ResultOfT : never;
export declare function isTupleLike<T extends Array<TypeGuard<unknown>>>(value: unknown, guards: [...T]): value is ResolveTypesOfTypeGuardsTuple<T>;
export declare function isTypedArray(value: unknown): value is TypedArray;
export declare function isUint16Array(value: unknown): value is Uint16Array;
export declare function isUint32Array(value: unknown): value is Uint32Array;
export declare function isUint8Array(value: unknown): value is Uint8Array;
export declare function isUint8ClampedArray(value: unknown): value is Uint8ClampedArray;
export declare function isUndefined(value: unknown): value is undefined;
export declare function isUrlInstance(value: unknown): value is URL;
export declare function isUrlSearchParams(value: unknown): value is URLSearchParams;
export declare function isUrlString(value: unknown): value is string;
export declare function isValidDate(value: unknown): value is Date;
export declare function isValidLength(value: unknown): value is number;
export declare function isWeakMap<Key extends object = object, Value = unknown>(value: unknown): value is WeakMap<Key, Value>;
export declare function isWeakRef(value: unknown): value is WeakRef<object>;
export declare function isWeakSet(value: unknown): value is WeakSet<object>;
export declare function isWhitespaceString(value: unknown): value is Whitespace;
type Assert = {
    undefined: (value: unknown, message?: string) => asserts value is undefined;
    string: (value: unknown, message?: string) => asserts value is string;
    number: (value: unknown, message?: string) => asserts value is number;
    positiveNumber: (value: unknown, message?: string) => asserts value is number;
    negativeNumber: (value: unknown, message?: string) => asserts value is number;
    bigint: (value: unknown, message?: string) => asserts value is bigint;
    function: (value: unknown, message?: string) => asserts value is Function;
    null: (value: unknown, message?: string) => asserts value is null;
    class: <T = unknown>(value: unknown, message?: string) => asserts value is Class<T>;
    boolean: (value: unknown, message?: string) => asserts value is boolean;
    symbol: (value: unknown, message?: string) => asserts value is symbol;
    numericString: (value: unknown, message?: string) => asserts value is `${number}`;
    array: <T = unknown>(value: unknown, assertion?: (element: unknown) => asserts element is T, message?: string) => asserts value is T[];
    buffer: (value: unknown, message?: string) => asserts value is NodeBuffer;
    blob: (value: unknown, message?: string) => asserts value is Blob;
    nullOrUndefined: (value: unknown, message?: string) => asserts value is null | undefined;
    object: <Key extends keyof any = string, Value = unknown>(value: unknown, message?: string) => asserts value is Record<Key, Value>;
    iterable: <T = unknown>(value: unknown, message?: string) => asserts value is Iterable<T>;
    asyncIterable: <T = unknown>(value: unknown, message?: string) => asserts value is AsyncIterable<T>;
    generator: (value: unknown, message?: string) => asserts value is Generator;
    asyncGenerator: (value: unknown, message?: string) => asserts value is AsyncGenerator;
    nativePromise: <T = unknown>(value: unknown, message?: string) => asserts value is Promise<T>;
    promise: <T = unknown>(value: unknown, message?: string) => asserts value is Promise<T>;
    generatorFunction: (value: unknown, message?: string) => asserts value is GeneratorFunction;
    asyncGeneratorFunction: (value: unknown, message?: string) => asserts value is AsyncGeneratorFunction;
    asyncFunction: (value: unknown, message?: string) => asserts value is Function;
    boundFunction: (value: unknown, message?: string) => asserts value is Function;
    regExp: (value: unknown, message?: string) => asserts value is RegExp;
    date: (value: unknown, message?: string) => asserts value is Date;
    error: (value: unknown, message?: string) => asserts value is Error;
    map: <Key = unknown, Value = unknown>(value: unknown, message?: string) => asserts value is Map<Key, Value>;
    set: <T = unknown>(value: unknown, message?: string) => asserts value is Set<T>;
    weakMap: <Key extends object = object, Value = unknown>(value: unknown, message?: string) => asserts value is WeakMap<Key, Value>;
    weakSet: <T extends object = object>(value: unknown, message?: string) => asserts value is WeakSet<T>;
    weakRef: <T extends object = object>(value: unknown, message?: string) => asserts value is WeakRef<T>;
    int8Array: (value: unknown, message?: string) => asserts value is Int8Array;
    uint8Array: (value: unknown, message?: string) => asserts value is Uint8Array;
    uint8ClampedArray: (value: unknown, message?: string) => asserts value is Uint8ClampedArray;
    int16Array: (value: unknown, message?: string) => asserts value is Int16Array;
    uint16Array: (value: unknown, message?: string) => asserts value is Uint16Array;
    int32Array: (value: unknown, message?: string) => asserts value is Int32Array;
    uint32Array: (value: unknown, message?: string) => asserts value is Uint32Array;
    float32Array: (value: unknown, message?: string) => asserts value is Float32Array;
    float64Array: (value: unknown, message?: string) => asserts value is Float64Array;
    bigInt64Array: (value: unknown, message?: string) => asserts value is BigInt64Array;
    bigUint64Array: (value: unknown, message?: string) => asserts value is BigUint64Array;
    arrayBuffer: (value: unknown, message?: string) => asserts value is ArrayBuffer;
    sharedArrayBuffer: (value: unknown, message?: string) => asserts value is SharedArrayBuffer;
    dataView: (value: unknown, message?: string) => asserts value is DataView;
    enumCase: <T = unknown>(value: unknown, targetEnum: T, message?: string) => asserts value is T[keyof T];
    urlInstance: (value: unknown, message?: string) => asserts value is URL;
    urlString: (value: unknown, message?: string) => asserts value is string;
    truthy: <T>(value: T | Falsy, message?: string) => asserts value is T;
    falsy: (value: unknown, message?: string) => asserts value is Falsy;
    nan: (value: unknown, message?: string) => asserts value is number;
    primitive: (value: unknown, message?: string) => asserts value is Primitive;
    integer: (value: unknown, message?: string) => asserts value is number;
    safeInteger: (value: unknown, message?: string) => asserts value is number;
    plainObject: <Value = unknown>(value: unknown, message?: string) => asserts value is Record<PropertyKey, Value>;
    typedArray: (value: unknown, message?: string) => asserts value is TypedArray;
    arrayLike: <T = unknown>(value: unknown, message?: string) => asserts value is ArrayLike<T>;
    tupleLike: <T extends Array<TypeGuard<unknown>>>(value: unknown, guards: [...T], message?: string) => asserts value is ResolveTypesOfTypeGuardsTuple<T>;
    htmlElement: (value: unknown, message?: string) => asserts value is HTMLElement;
    observable: (value: unknown, message?: string) => asserts value is ObservableLike;
    nodeStream: (value: unknown, message?: string) => asserts value is NodeStream;
    infinite: (value: unknown, message?: string) => asserts value is number;
    emptyArray: (value: unknown, message?: string) => asserts value is never[];
    nonEmptyArray: <T = unknown, Item = unknown>(value: T | Item[], message?: string) => asserts value is [Item, ...Item[]];
    emptyString: (value: unknown, message?: string) => asserts value is '';
    emptyStringOrWhitespace: (value: unknown, message?: string) => asserts value is '' | Whitespace;
    nonEmptyString: (value: unknown, message?: string) => asserts value is string;
    nonEmptyStringAndNotWhitespace: (value: unknown, message?: string) => asserts value is string;
    emptyObject: <Key extends keyof any = string>(value: unknown, message?: string) => asserts value is Record<Key, never>;
    nonEmptyObject: <Key extends keyof any = string, Value = unknown>(value: unknown, message?: string) => asserts value is Record<Key, Value>;
    emptySet: (value: unknown, message?: string) => asserts value is Set<never>;
    nonEmptySet: <T = unknown>(value: unknown, message?: string) => asserts value is Set<T>;
    emptyMap: (value: unknown, message?: string) => asserts value is Map<never, never>;
    nonEmptyMap: <Key = unknown, Value = unknown>(value: unknown, message?: string) => asserts value is Map<Key, Value>;
    propertyKey: (value: unknown, message?: string) => asserts value is PropertyKey;
    formData: (value: unknown, message?: string) => asserts value is FormData;
    urlSearchParams: (value: unknown, message?: string) => asserts value is URLSearchParams;
    validDate: (value: unknown, message?: string) => asserts value is Date;
    validLength: (value: unknown, message?: string) => asserts value is number;
    whitespaceString: (value: unknown, message?: string) => asserts value is string;
    evenInteger: (value: number, message?: string) => asserts value is number;
    oddInteger: (value: number, message?: string) => asserts value is number;
    directInstanceOf: <T>(instance: unknown, class_: Class<T>, message?: string) => asserts instance is T;
    inRange: (value: number, range: number | [number, number], message?: string) => asserts value is number;
    any: (predicate: Predicate | Predicate[], ...values: unknown[]) => void | never;
    all: (predicate: Predicate, ...values: unknown[]) => void | never;
};
export declare const assert: Assert;
export declare function assertAll(predicate: Predicate, ...values: unknown[]): void | never;
export declare function assertAny(predicate: Predicate | Predicate[], ...values: unknown[]): void | never;
export declare function assertArray<T = unknown>(value: unknown, assertion?: (element: unknown, message?: string) => asserts element is T, message?: string): asserts value is T[];
export declare function assertArrayBuffer(value: unknown, message?: string): asserts value is ArrayBuffer;
export declare function assertArrayLike<T = unknown>(value: unknown, message?: string): asserts value is ArrayLike<T>;
export declare function assertAsyncFunction(value: unknown, message?: string): asserts value is Function;
export declare function assertAsyncGenerator(value: unknown, message?: string): asserts value is AsyncGenerator;
export declare function assertAsyncGeneratorFunction(value: unknown, message?: string): asserts value is AsyncGeneratorFunction;
export declare function assertAsyncIterable<T = unknown>(value: unknown, message?: string): asserts value is AsyncIterable<T>;
export declare function assertBigint(value: unknown, message?: string): asserts value is bigint;
export declare function assertBigInt64Array(value: unknown, message?: string): asserts value is BigInt64Array;
export declare function assertBigUint64Array(value: unknown, message?: string): asserts value is BigUint64Array;
export declare function assertBlob(value: unknown, message?: string): asserts value is Blob;
export declare function assertBoolean(value: unknown, message?: string): asserts value is boolean;
export declare function assertBoundFunction(value: unknown, message?: string): asserts value is Function;
/**
Note: [Prefer using `Uint8Array` instead of `Buffer`.](https://sindresorhus.com/blog/goodbye-nodejs-buffer)
*/
export declare function assertBuffer(value: unknown, message?: string): asserts value is NodeBuffer;
export declare function assertClass<T>(value: unknown, message?: string): asserts value is Class<T>;
export declare function assertDataView(value: unknown, message?: string): asserts value is DataView;
export declare function assertDate(value: unknown, message?: string): asserts value is Date;
export declare function assertDirectInstanceOf<T>(instance: unknown, class_: Class<T>, message?: string): asserts instance is T;
export declare function assertEmptyArray(value: unknown, message?: string): asserts value is never[];
export declare function assertEmptyMap(value: unknown, message?: string): asserts value is Map<never, never>;
export declare function assertEmptyObject<Key extends keyof any = string>(value: unknown, message?: string): asserts value is Record<Key, never>;
export declare function assertEmptySet(value: unknown, message?: string): asserts value is Set<never>;
export declare function assertEmptyString(value: unknown, message?: string): asserts value is '';
export declare function assertEmptyStringOrWhitespace(value: unknown, message?: string): asserts value is '' | Whitespace;
export declare function assertEnumCase<T = unknown>(value: unknown, targetEnum: T, message?: string): asserts value is T[keyof T];
export declare function assertError(value: unknown, message?: string): asserts value is Error;
export declare function assertEvenInteger(value: number, message?: string): asserts value is number;
export declare function assertFalsy(value: unknown, message?: string): asserts value is Falsy;
export declare function assertFloat32Array(value: unknown, message?: string): asserts value is Float32Array;
export declare function assertFloat64Array(value: unknown, message?: string): asserts value is Float64Array;
export declare function assertFormData(value: unknown, message?: string): asserts value is FormData;
export declare function assertFunction(value: unknown, message?: string): asserts value is Function;
export declare function assertGenerator(value: unknown, message?: string): asserts value is Generator;
export declare function assertGeneratorFunction(value: unknown, message?: string): asserts value is GeneratorFunction;
export declare function assertHtmlElement(value: unknown, message?: string): asserts value is HTMLElement;
export declare function assertInfinite(value: unknown, message?: string): asserts value is number;
export declare function assertInRange(value: number, range: number | [number, number], message?: string): asserts value is number;
export declare function assertInt16Array(value: unknown, message?: string): asserts value is Int16Array;
export declare function assertInt32Array(value: unknown, message?: string): asserts value is Int32Array;
export declare function assertInt8Array(value: unknown, message?: string): asserts value is Int8Array;
export declare function assertInteger(value: unknown, message?: string): asserts value is number;
export declare function assertIterable<T = unknown>(value: unknown, message?: string): asserts value is Iterable<T>;
export declare function assertMap<Key = unknown, Value = unknown>(value: unknown, message?: string): asserts value is Map<Key, Value>;
export declare function assertNan(value: unknown, message?: string): asserts value is number;
export declare function assertNativePromise<T = unknown>(value: unknown, message?: string): asserts value is Promise<T>;
export declare function assertNegativeNumber(value: unknown, message?: string): asserts value is number;
export declare function assertNodeStream(value: unknown, message?: string): asserts value is NodeStream;
export declare function assertNonEmptyArray<T = unknown, Item = unknown>(value: T | Item[], message?: string): asserts value is [Item, ...Item[]];
export declare function assertNonEmptyMap<Key = unknown, Value = unknown>(value: unknown, message?: string): asserts value is Map<Key, Value>;
export declare function assertNonEmptyObject<Key extends keyof any = string, Value = unknown>(value: unknown, message?: string): asserts value is Record<Key, Value>;
export declare function assertNonEmptySet<T = unknown>(value: unknown, message?: string): asserts value is Set<T>;
export declare function assertNonEmptyString(value: unknown, message?: string): asserts value is string;
export declare function assertNonEmptyStringAndNotWhitespace(value: unknown, message?: string): asserts value is string;
export declare function assertNull(value: unknown, message?: string): asserts value is null;
export declare function assertNullOrUndefined(value: unknown, message?: string): asserts value is null | undefined;
export declare function assertNumber(value: unknown, message?: string): asserts value is number;
export declare function assertNumericString(value: unknown, message?: string): asserts value is `${number}`;
export declare function assertObject(value: unknown, message?: string): asserts value is object;
export declare function assertObservable(value: unknown, message?: string): asserts value is ObservableLike;
export declare function assertOddInteger(value: number, message?: string): asserts value is number;
export declare function assertPlainObject<Value = unknown>(value: unknown, message?: string): asserts value is Record<PropertyKey, Value>;
export declare function assertPositiveNumber(value: unknown, message?: string): asserts value is number;
export declare function assertPrimitive(value: unknown, message?: string): asserts value is Primitive;
export declare function assertPromise<T = unknown>(value: unknown, message?: string): asserts value is Promise<T>;
export declare function assertPropertyKey(value: unknown, message?: string): asserts value is number;
export declare function assertRegExp(value: unknown, message?: string): asserts value is RegExp;
export declare function assertSafeInteger(value: unknown, message?: string): asserts value is number;
export declare function assertSet<T = unknown>(value: unknown, message?: string): asserts value is Set<T>;
export declare function assertSharedArrayBuffer(value: unknown, message?: string): asserts value is SharedArrayBuffer;
export declare function assertString(value: unknown, message?: string): asserts value is string;
export declare function assertSymbol(value: unknown, message?: string): asserts value is symbol;
export declare function assertTruthy<T>(value: T | Falsy, message?: string): asserts value is T;
export declare function assertTupleLike<T extends Array<TypeGuard<unknown>>>(value: unknown, guards: [...T], message?: string): asserts value is ResolveTypesOfTypeGuardsTuple<T>;
export declare function assertTypedArray(value: unknown, message?: string): asserts value is TypedArray;
export declare function assertUint16Array(value: unknown, message?: string): asserts value is Uint16Array;
export declare function assertUint32Array(value: unknown, message?: string): asserts value is Uint32Array;
export declare function assertUint8Array(value: unknown, message?: string): asserts value is Uint8Array;
export declare function assertUint8ClampedArray(value: unknown, message?: string): asserts value is Uint8ClampedArray;
export declare function assertUndefined(value: unknown, message?: string): asserts value is undefined;
export declare function assertUrlInstance(value: unknown, message?: string): asserts value is URL;
export declare function assertUrlSearchParams(value: unknown, message?: string): asserts value is URLSearchParams;
export declare function assertUrlString(value: unknown, message?: string): asserts value is string;
export declare function assertValidDate(value: unknown, message?: string): asserts value is Date;
export declare function assertValidLength(value: unknown, message?: string): asserts value is number;
export declare function assertWeakMap<Key extends object = object, Value = unknown>(value: unknown, message?: string): asserts value is WeakMap<Key, Value>;
export declare function assertWeakRef<T extends object = object>(value: unknown, message?: string): asserts value is WeakRef<T>;
export declare function assertWeakSet<T extends object = object>(value: unknown, message?: string): asserts value is WeakSet<T>;
export declare function assertWhitespaceString(value: unknown, message?: string): asserts value is string;
export default is;
export type { ArrayLike, Class, NodeStream, ObservableLike, Predicate, Primitive, TypedArray, } from './types.js';
