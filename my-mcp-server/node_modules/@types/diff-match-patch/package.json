{"name": "@types/diff-match-patch", "version": "1.0.36", "description": "TypeScript definitions for diff-match-patch", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/diff-match-patch", "license": "MIT", "contributors": [{"name": "<PERSON><PERSON>", "url": "https://asana.com"}, {"name": "<PERSON>", "githubUsername": "Methuselah96", "url": "https://github.com/Methuselah96"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/diff-match-patch"}, "scripts": {}, "dependencies": {}, "typesPublisherContentHash": "f481bf215255fe860cdb93afd91dd7d6154c257fa0e506efa6d7417efcc377a7", "typeScriptVersion": "4.5"}