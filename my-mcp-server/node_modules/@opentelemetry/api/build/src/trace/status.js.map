{"version": 3, "file": "status.js", "sourceRoot": "", "sources": ["../../../src/trace/status.ts"], "names": [], "mappings": ";;;AAsBA;;GAEG;AACH,IAAY,cAcX;AAdD,WAAY,cAAc;IACxB;;OAEG;IACH,qDAAS,CAAA;IACT;;;OAGG;IACH,+CAAM,CAAA;IACN;;OAEG;IACH,qDAAS,CAAA;AACX,CAAC,EAdW,cAAc,GAAd,sBAAc,KAAd,sBAAc,QAczB", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nexport interface SpanStatus {\n  /** The status code of this message. */\n  code: SpanStatusCode;\n  /** A developer-facing error message. */\n  message?: string;\n}\n\n/**\n * An enumeration of status codes.\n */\nexport enum SpanStatusCode {\n  /**\n   * The default status.\n   */\n  UNSET = 0,\n  /**\n   * The operation has been validated by an Application developer or\n   * Operator to have completed successfully.\n   */\n  OK = 1,\n  /**\n   * The operation contains an error.\n   */\n  ERROR = 2,\n}\n"]}