# WordPress to Cloudflare D1 Migration Instructions

## Files Generated:
- 01-categories.sql: Sample categories data
- 02-users.sql: Sample users data  
- 03-posts.sql: Sample posts data
- 04-post-categories.sql: Post-category relationships

## Migration Steps:

1. **Setup the D1 database** (if not already done):
   ```bash
   # Create the database schema
   wrangler d1 execute blog-database --local --file=database-schema.sql
   
   # Run migrations
   wrangler d1 execute blog-database --local --file=migrations/001_initial_schema.sql
   wrangler d1 execute blog-database --local --file=migrations/002_add_indexes.sql
   wrangler d1 execute blog-database --local --file=migrations/003_default_data.sql
   ```

2. **Execute the migration files in order**:
   ```bash
   # Categories first
   wrangler d1 execute blog-database --local --file=migration-output/01-categories.sql
   
   # Users second
   wrangler d1 execute blog-database --local --file=migration-output/02-users.sql
   
   # Posts third
   wrangler d1 execute blog-database --local --file=migration-output/03-posts.sql
   
   # Post-category relationships last
   wrangler d1 execute blog-database --local --file=migration-output/04-post-categories.sql
   ```

3. **Test the migration**:
   ```bash
   # Test database connection
   npm run db:test
   
   # Start the development server
   npm run dev
   ```

4. **For production deployment**:
   ```bash
   # Remove --local flag to execute on production database
   wrangler d1 execute blog-database --file=migration-output/01-categories.sql
   wrangler d1 execute blog-database --file=migration-output/02-users.sql
   wrangler d1 execute blog-database --file=migration-output/03-posts.sql
   wrangler d1 execute blog-database --file=migration-output/04-post-categories.sql
   ```

## Sample Data Included:
- 5 categories (Uncategorized, Technology, Tutorials, News, Reviews)
- 2 users (admin, dhananjay)
- 5 posts (including welcome post, tutorial, privacy policy, Christmas wishes, D1 guide)
- Post-category relationships

## Next Steps:
1. Update environment variables in .env.local
2. Test the blog frontend with the migrated data
3. Customize the content as needed
4. Add more posts and categories as required

## Notes:
- This is sample data based on the WordPress structure
- You can modify the content in the SQL files before executing
- The blog frontend should now display the migrated content
- Images and media files need to be migrated separately
