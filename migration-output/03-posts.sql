INSERT INTO posts (id, title, slug, content, excerpt, author_id, status, post_type, published_at, created_at, updated_at) VALUES (1, 'Welcome to ZayoTech Blog', 'welcome-to-zayotech-blog', '<!-- wp:paragraph -->
<p>Welcome to our technology blog! Here you''ll find the latest updates, tutorials, and insights about web development, programming, and digital innovation.</p>
<!-- /wp:paragraph -->

<!-- wp:paragraph -->
<p>We cover topics ranging from frontend frameworks like React and Next.js to backend technologies, databases, and cloud computing.</p>
<!-- /wp:paragraph -->

<!-- wp:heading -->
<h2>What You Can Expect</h2>
<!-- /wp:heading -->

<!-- wp:list -->
<ul>
<li>In-depth tutorials</li>
<li>Technology reviews</li>
<li>Industry news and trends</li>
<li>Best practices and tips</li>
</ul>
<!-- /wp:list -->', 'Welcome to our technology blog! Here you''ll find the latest updates, tutorials, and insights about web development.', 1, 'published', 'post', datetime('now'), datetime('now'), datetime('now'));

INSERT INTO posts (id, title, slug, content, excerpt, author_id, status, post_type, published_at, created_at, updated_at) VALUES (2, 'Getting Started with Next.js 15', 'getting-started-with-nextjs-15', '<!-- wp:paragraph -->
<p>Next.js 15 brings exciting new features and improvements to the React framework. In this tutorial, we''ll explore the key changes and how to get started.</p>
<!-- /wp:paragraph -->

<!-- wp:heading -->
<h2>New Features in Next.js 15</h2>
<!-- /wp:heading -->

<!-- wp:paragraph -->
<p>The latest version includes enhanced App Router, improved performance, and better developer experience.</p>
<!-- /wp:paragraph -->

<!-- wp:code -->
<pre class="wp-block-code"><code>npx create-next-app@latest my-app
cd my-app
npm run dev</code></pre>
<!-- /wp:code -->

<!-- wp:paragraph -->
<p>This will create a new Next.js 15 application with all the latest features enabled by default.</p>
<!-- /wp:paragraph -->', 'Next.js 15 brings exciting new features and improvements to the React framework. Learn how to get started.', 2, 'published', 'post', datetime('now'), datetime('now'), datetime('now'));

INSERT INTO posts (id, title, slug, content, excerpt, author_id, status, post_type, published_at, created_at, updated_at) VALUES (3, 'Privacy Policy', 'privacy-policy', '<!-- wp:heading -->
<h2>Who we are</h2>
<!-- /wp:heading -->

<!-- wp:paragraph -->
<p>Our website address is: https://zayotech.com.</p>
<!-- /wp:paragraph -->

<!-- wp:heading -->
<h2>Comments</h2>
<!-- /wp:heading -->

<!-- wp:paragraph -->
<p>When visitors leave comments on the site, we collect the data shown in the comments form, as well as the visitor''s IP address and browser user agent string to help with spam detection.</p>
<!-- /wp:paragraph -->', 'Our privacy policy explains how we collect, use, and protect your personal information.', 1, 'published', 'page', datetime('now'), datetime('now'), datetime('now'));

INSERT INTO posts (id, title, slug, content, excerpt, author_id, status, post_type, published_at, created_at, updated_at) VALUES (4, 'Merry Christmas Wishes and Christmas Cards', 'merry-christmas-wishes-and-christmas-cards', '<!-- wp:paragraph -->
<p>It''s the holiday season, and there''s no better way to make people happy than to send them warm Merry Christmas wishes and lovely Christmas cards.</p>
<!-- /wp:paragraph -->

<!-- wp:heading -->
<h2>Merry Christmas Wishes</h2>
<!-- /wp:heading -->

<!-- wp:quote -->
<blockquote class="wp-block-quote">
<p>Wishing you love, joy, and peace this Christmas.</p>
</blockquote>
<!-- /wp:quote -->

<!-- wp:quote -->
<blockquote class="wp-block-quote">
<p>May this season find you among those you love, sharing in the twin glories of generosity and gratitude.</p>
</blockquote>
<!-- /wp:quote -->', 'Collection of beautiful Christmas wishes and cards to share with your loved ones.', 2, 'published', 'post', datetime('now'), datetime('now'), datetime('now'));

INSERT INTO posts (id, title, slug, content, excerpt, author_id, status, post_type, published_at, created_at, updated_at) VALUES (5, 'Understanding Cloudflare D1 Database', 'understanding-cloudflare-d1-database', '<!-- wp:paragraph -->
<p>Cloudflare D1 is a serverless SQL database built on SQLite, designed for edge computing and global distribution.</p>
<!-- /wp:paragraph -->

<!-- wp:heading -->
<h2>Key Features</h2>
<!-- /wp:heading -->

<!-- wp:list -->
<ul>
<li>Global distribution</li>
<li>Zero-latency reads</li>
<li>SQL compatibility</li>
<li>Serverless architecture</li>
</ul>
<!-- /wp:list -->

<!-- wp:paragraph -->
<p>Perfect for modern web applications that need fast, reliable data access from anywhere in the world.</p>
<!-- /wp:paragraph -->', 'Learn about Cloudflare D1, a serverless SQL database built for edge computing.', 1, 'draft', 'post', NULL, datetime('now'), datetime('now'));