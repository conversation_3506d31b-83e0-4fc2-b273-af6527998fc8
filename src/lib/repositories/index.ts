/**
 * Repository exports
 */

import { BaseRepository } from './base';
import { PostsRepository } from './posts';
import { CategoriesRepository } from './categories';
import { UsersRepository } from './users';

// Re-export classes
export { BaseRepository, PostsRepository, CategoriesRepository, UsersRepository };

// Create singleton instances
export const postsRepository = new PostsRepository();
export const categoriesRepository = new CategoriesRepository();
export const usersRepository = new UsersRepository();

// Export types
export type {
  CreatePostData,
  UpdatePostData,
  PostFilters,
} from './posts';

export type {
  CreateCategoryData,
  UpdateCategoryData,
} from './categories';

export type {
  CreateUserData,
  UpdateUserData,
} from './users';
