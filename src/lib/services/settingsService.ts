/**
 * Settings service for database operations
 */

import { db } from '../database';
import type { Setting } from '../database';

export interface SiteSettings {
  site_title: string;
  site_description: string;
  site_url: string;
  posts_per_page: number;
  comment_moderation: boolean;
  default_language: 'en' | 'hi';
  theme: string;
  timezone: string;
}

export class SettingsService {
  private cache: Map<string, any> = new Map();
  private cacheExpiry: Map<string, number> = new Map();
  private readonly CACHE_TTL = 5 * 60 * 1000; // 5 minutes

  /**
   * Get a setting value by key
   */
  async getSetting(key: string): Promise<string | null> {
    // Check cache first
    if (this.isCached(key)) {
      return this.cache.get(key);
    }

    const query = 'SELECT value FROM settings WHERE key = ?';
    const result = await db.queryFirst(query, [key]);
    
    const value = result?.value || null;
    
    // Cache the result
    this.setCacheValue(key, value);
    
    return value;
  }

  /**
   * Get multiple settings
   */
  async getSettings(keys: string[]): Promise<Record<string, string | null>> {
    const settings: Record<string, string | null> = {};
    const uncachedKeys: string[] = [];

    // Check cache for each key
    for (const key of keys) {
      if (this.isCached(key)) {
        settings[key] = this.cache.get(key);
      } else {
        uncachedKeys.push(key);
      }
    }

    // Fetch uncached settings from database
    if (uncachedKeys.length > 0) {
      const placeholders = uncachedKeys.map(() => '?').join(',');
      const query = `SELECT key, value FROM settings WHERE key IN (${placeholders})`;
      const result = await db.query(query, uncachedKeys);
      
      const dbSettings = result.results || [];
      
      // Process database results
      for (const setting of dbSettings) {
        settings[setting.key] = setting.value;
        this.setCacheValue(setting.key, setting.value);
      }

      // Set null for keys not found in database
      for (const key of uncachedKeys) {
        if (!(key in settings)) {
          settings[key] = null;
          this.setCacheValue(key, null);
        }
      }
    }

    return settings;
  }

  /**
   * Get all site settings as a typed object
   */
  async getSiteSettings(): Promise<SiteSettings> {
    const keys = [
      'site_title',
      'site_description', 
      'site_url',
      'posts_per_page',
      'comment_moderation',
      'default_language',
      'theme',
      'timezone'
    ];

    const settings = await this.getSettings(keys);

    return {
      site_title: settings.site_title || 'Teknorial Blog',
      site_description: settings.site_description || 'Technology tutorials and programming guides',
      site_url: settings.site_url || 'https://teknorial.com',
      posts_per_page: parseInt(settings.posts_per_page || '12'),
      comment_moderation: settings.comment_moderation === 'true',
      default_language: (settings.default_language as 'en' | 'hi') || 'en',
      theme: settings.theme || 'teknorial',
      timezone: settings.timezone || 'UTC',
    };
  }

  /**
   * Set a setting value
   */
  async setSetting(key: string, value: string): Promise<void> {
    const query = `
      INSERT INTO settings (key, value, updated_at)
      VALUES (?, ?, CURRENT_TIMESTAMP)
      ON CONFLICT(key) DO UPDATE SET
        value = excluded.value,
        updated_at = excluded.updated_at
    `;

    await db.execute(query, [key, value]);
    
    // Update cache
    this.setCacheValue(key, value);
  }

  /**
   * Set multiple settings
   */
  async setSettings(settings: Record<string, string>): Promise<void> {
    const entries = Object.entries(settings);
    
    for (const [key, value] of entries) {
      await this.setSetting(key, value);
    }
  }

  /**
   * Delete a setting
   */
  async deleteSetting(key: string): Promise<boolean> {
    const result = await db.execute('DELETE FROM settings WHERE key = ?', [key]);
    
    // Remove from cache
    this.cache.delete(key);
    this.cacheExpiry.delete(key);
    
    return result.success;
  }

  /**
   * Get all settings
   */
  async getAllSettings(): Promise<Setting[]> {
    const query = 'SELECT * FROM settings ORDER BY key';
    const result = await db.query(query);
    return result.results || [];
  }

  /**
   * Clear settings cache
   */
  clearCache(): void {
    this.cache.clear();
    this.cacheExpiry.clear();
  }

  /**
   * Check if a key is cached and not expired
   */
  private isCached(key: string): boolean {
    if (!this.cache.has(key)) {
      return false;
    }

    const expiry = this.cacheExpiry.get(key);
    if (!expiry || Date.now() > expiry) {
      this.cache.delete(key);
      this.cacheExpiry.delete(key);
      return false;
    }

    return true;
  }

  /**
   * Set a value in cache with expiry
   */
  private setCacheValue(key: string, value: any): void {
    this.cache.set(key, value);
    this.cacheExpiry.set(key, Date.now() + this.CACHE_TTL);
  }

  /**
   * Initialize default settings if they don't exist
   */
  async initializeDefaults(): Promise<void> {
    const defaultSettings = {
      site_title: 'Teknorial Blog',
      site_description: 'Technology tutorials and programming guides',
      site_url: 'https://teknorial.com',
      posts_per_page: '12',
      comment_moderation: 'true',
      default_language: 'en',
      theme: 'teknorial',
      timezone: 'UTC',
    };

    const existingSettings = await this.getSettings(Object.keys(defaultSettings));
    
    for (const [key, defaultValue] of Object.entries(defaultSettings)) {
      if (existingSettings[key] === null) {
        await this.setSetting(key, defaultValue);
      }
    }
  }
}

// Export singleton instance
export const settingsService = new SettingsService();
